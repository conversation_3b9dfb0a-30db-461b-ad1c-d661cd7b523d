# 亮度调节自动化程序 API 使用指南

## 📖 概述

本API服务为亮度调节自动化程序提供远程控制功能，支持任务队列管理、设备控制和状态监控。

**核心特性：**
- 🚀 **预设功能**：通过预设名称快速创建队列，无需手动指定应用列表
- 🔄 **实时监控**：5秒心跳机制，实时监控系统状态
- 🛡️ **并发安全**：API操作与GUI操作互斥，确保数据一致性
- 📝 **完整日志**：按日期分割的API日志，自动清理过期文件

**服务信息：**
- **地址**: http://127.0.0.1:9080
- **协议**: REST API (JSON格式)
- **心跳频率**: 5秒
- **日志保留**: 30天

## 🚀 快速开始

### 1. 启动服务
运行主程序后，API服务会自动启动：
```bash
python main.py
```
看到 "API服务器已启动 - http://127.0.0.1:9080" 表示启动成功。

### 2. 验证服务
```bash
curl http://127.0.0.1:9080/api/health
```

### 3. 获取预设列表
```bash
curl http://127.0.0.1:9080/api/presets
```

### 4. 使用预设创建队列
```bash
curl -X POST http://127.0.0.1:9080/api/queue/add/5.0 \
  -H "Content-Type: application/json" \
  -d '{"preset":"内_OLED_自动"}'
```

## 🎯 预设功能详解

### 为什么使用预设？
- **简化操作**：只需指定预设名称，无需记忆复杂的应用列表
- **减少错误**：预设确保应用列表和设置的一致性
- **提高效率**：一键创建常用的测试队列配置

### 可用预设列表

#### 5.0版本预设
| 预设名称 | 说明 | 自动亮度 |
|---------|------|----------|
| 内_OLED_自动 | 内销OLED屏幕自动亮度测试 | ✅ |
| 内_OLED_手动 | 内销OLED屏幕手动亮度测试 | ❌ |
| 外_OLED_自动 | 外销OLED屏幕自动亮度测试 | ✅ |
| 外_OLED_手动 | 外销OLED屏幕手动亮度测试 | ❌ |
| 内_LCD_自动 | 内销LCD屏幕自动亮度测试 | ✅ |
| 内_LCD_手动 | 内销LCD屏幕手动亮度测试 | ❌ |
| 外_LCD_自动 | 外销LCD屏幕自动亮度测试 | ✅ |
| 外_LCD_手动 | 外销LCD屏幕手动亮度测试 | ❌ |

#### 4.0版本预设
| 预设名称 | 说明 | 自动亮度 |
|---------|------|----------|
| 内销自动 | 内销市场自动亮度测试 | ✅ |
| 内销手动 | 内销市场手动亮度测试 | ❌ |
| 外销自动 | 外销市场自动亮度测试 | ✅ |
| 外销手动 | 外销市场手动亮度测试 | ❌ |

## 📋 API接口详解

### 基础接口

#### 健康检查
```http
GET /api/health
```
**用途**：检查服务状态和获取心跳信息
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "health": "healthy",
    "heartbeat": {
      "status": "alive",
      "timestamp": "2025-07-04T10:30:00Z",
      "uptime": 3600,
      "queue_status": "idle",
      "execution_status": "completed",
      "device_connected": true,
      "has_errors": false,
      "api_requests_count": 15
    }
  }
}
```

**新增心跳字段说明**：
- `execution_status`: 详细的执行状态（idle/running/completed/failed/stopped）
- `has_errors`: 是否存在执行错误

#### 系统状态
```http
GET /api/status
```
**用途**：获取系统整体状态  
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "queue": {
      "queue_count": 2,
      "is_running": false,
      "current_queue_index": -1
    },
    "devices": ["device_id_1"],
    "device_connected": true
  }
}
```

#### 获取预设列表
```http
GET /api/presets
```
**用途**：获取所有可用的预设配置  
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "presets": {
      "5.0": ["内_OLED_自动", "内_OLED_手动", "外_OLED_自动", "外_OLED_手动"],
      "4.0": ["内销自动", "内销手动", "外销自动", "外销手动"]
    }
  }
}
```

### 队列管理接口

#### 获取队列信息
```http
GET /api/queue
```
**用途**：获取当前队列状态和详细列表  
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "queue_status": {
      "queue_count": 1,
      "is_running": false,
      "is_empty": false
    },
    "queue_list": [
      {
        "index": 0,
        "title": "5.0 - 内_OLED_自动",
        "type": "5.0",
        "apps": ["微信", "抖音", "王者荣耀"],
        "is_auto_brightness": true
      }
    ]
  }
}
```

#### 添加5.0版本队列
```http
POST /api/queue/add/5.0
```
**用途**：添加5.0版本测试队列

**使用方式1：预设方式（推荐）**
```json
{
  "preset": "内_OLED_自动"
}
```

**使用方式2：应用列表方式**
```json
{
  "apps": ["微信", "抖音", "王者荣耀"],
  "auto_brightness": false
}
```

**使用方式3：预设+自定义**
```json
{
  "preset": "内_OLED_自动",
  "auto_brightness": false
}
```

**参数说明**：
- `preset`: 预设名称（可选）
- `apps`: 应用列表（可选，如果提供preset则可为空）
- `auto_brightness`: 是否自动亮度（可选，预设会自动设置）

#### 添加4.0版本队列
```http
POST /api/queue/add/4.0
```
**用途**：添加4.0版本测试队列

**使用方式1：预设方式（推荐）**
```json
{
  "preset": "内销自动",
  "brightness_values": [30, 100, 300]
}
```

**使用方式2：应用列表方式**
```json
{
  "apps": ["微信", "抖音"],
  "brightness_values": [30, 100, 300],
  "auto_brightness": false
}
```

**参数说明**：
- `preset`: 预设名称（可选）
- `apps`: 应用列表（可选，如果提供preset则可为空）
- `brightness_values`: 亮度值列表（必需）
- `auto_brightness`: 是否自动亮度（可选，预设会自动设置）

#### 启动队列执行
```http
POST /api/queue/start
```
**用途**：启动队列执行
**请求体**（可选）：
```json
{
  "device_id": "specific_device_id"
}
```

#### 停止队列执行
```http
POST /api/queue/stop
```
**用途**：停止当前队列执行

#### 清空队列
```http
DELETE /api/queue/clear
```
**用途**：清空所有待执行的队列

### 任务监控接口

#### 获取执行状态
```http
GET /api/execution/status
```
**用途**：获取详细的任务执行状态信息
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "execution_summary": {
      "execution_status": "running",
      "total_queues": 2,
      "completed_queues": 1,
      "failed_queues": 0,
      "current_queue_index": 1,
      "is_running": true,
      "start_time": "2025-07-04T10:30:00Z",
      "duration_seconds": 120,
      "error_count": 0
    },
    "detailed_queues": [
      {
        "index": 0,
        "title": "5.0 - 内_OLED_自动",
        "status": "completed",
        "is_current": false
      },
      {
        "index": 1,
        "title": "4.0 - 内销手动",
        "status": "running",
        "is_current": true
      }
    ]
  }
}
```

**执行状态说明**：
- `idle`: 空闲状态，未开始执行
- `running`: 正在执行中
- `completed`: 所有队列执行完成
- `failed`: 执行过程中出现错误
- `stopped`: 用户手动停止

**队列状态说明**：
- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 已完成
- `failed`: 执行失败
- `skipped`: 跳过（因前面失败或停止）

#### 获取执行错误
```http
GET /api/execution/errors
```
**用途**：获取执行过程中的错误信息
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "error_count": 1,
    "errors": [
      {
        "timestamp": "2025-07-04T10:35:00Z",
        "queue_index": 1,
        "queue_title": "4.0 - 内销手动",
        "error_type": "ConnectionError",
        "error_message": "设备连接失败"
      }
    ],
    "last_error": {
      "timestamp": "2025-07-04T10:35:00Z",
      "queue_index": 1,
      "queue_title": "4.0 - 内销手动",
      "error_type": "ConnectionError",
      "error_message": "设备连接失败"
    }
  }
}
```

#### 检查执行完成状态
```http
GET /api/execution/completed
```
**用途**：快速检查任务是否已完成或失败
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "is_completed": true,
    "is_failed": false,
    "timestamp": "2025-07-04T10:40:00Z"
  }
}
```

### 设备管理接口

#### 获取设备列表
```http
GET /api/devices
```
**用途**：获取当前连接的设备列表
**响应示例**：
```json
{
  "status": "success",
  "data": {
    "devices": ["device_id_1", "device_id_2"],
    "device_count": 2
  }
}
```

## 💡 使用示例

### Python示例
```python
import requests

# 基础URL
api_url = "http://127.0.0.1:9080/api"

# 1. 检查服务状态
response = requests.get(f"{api_url}/health")
print("服务状态:", response.json())

# 2. 获取预设列表
response = requests.get(f"{api_url}/presets")
presets = response.json()["data"]["presets"]
print("可用预设:", presets)

# 3. 使用预设添加5.0队列
data = {"preset": "内_OLED_自动"}
response = requests.post(f"{api_url}/queue/add/5.0", json=data)
print("添加队列结果:", response.json())

# 4. 使用预设添加4.0队列
data = {
    "preset": "内销自动",
    "brightness_values": [30, 100, 300]
}
response = requests.post(f"{api_url}/queue/add/4.0", json=data)
print("添加队列结果:", response.json())

# 5. 查看队列状态
response = requests.get(f"{api_url}/queue")
print("队列状态:", response.json())

# 6. 启动执行
response = requests.post(f"{api_url}/queue/start")
print("启动结果:", response.json())

# 7. 监控执行状态
import time
while True:
    response = requests.get(f"{api_url}/execution/status")
    status_data = response.json()["data"]["execution_summary"]

    print(f"执行状态: {status_data['execution_status']}")
    print(f"进度: {status_data['completed_queues']}/{status_data['total_queues']}")

    # 检查是否完成
    if status_data["execution_status"] in ["completed", "failed", "stopped"]:
        break

    time.sleep(5)  # 5秒检查一次

# 8. 检查错误（如果有）
response = requests.get(f"{api_url}/execution/errors")
error_data = response.json()["data"]
if error_data["error_count"] > 0:
    print("执行过程中出现错误:", error_data["last_error"])
```

### curl示例
```bash
# 获取预设列表
curl http://127.0.0.1:9080/api/presets

# 使用预设添加5.0队列
curl -X POST http://127.0.0.1:9080/api/queue/add/5.0 \
  -H "Content-Type: application/json" \
  -d '{"preset":"内_OLED_自动"}'

# 使用预设添加4.0队列
curl -X POST http://127.0.0.1:9080/api/queue/add/4.0 \
  -H "Content-Type: application/json" \
  -d '{"preset":"内销自动","brightness_values":[30,100,300]}'

# 启动执行
curl -X POST http://127.0.0.1:9080/api/queue/start

# 查看队列状态
curl http://127.0.0.1:9080/api/queue

# 清空队列
curl -X DELETE http://127.0.0.1:9080/api/queue/clear

# 监控执行状态
curl http://127.0.0.1:9080/api/execution/status

# 检查执行错误
curl http://127.0.0.1:9080/api/execution/errors

# 检查是否完成
curl http://127.0.0.1:9080/api/execution/completed
```

## 🎯 最佳实践

### 推荐工作流程
1. **检查服务状态**：使用 `/api/health` 确认服务正常
2. **获取预设列表**：使用 `/api/presets` 查看可用预设
3. **创建队列**：优先使用预设方式创建队列
4. **检查队列**：使用 `/api/queue` 确认队列创建成功
5. **启动执行**：使用 `/api/queue/start` 开始测试
6. **监控状态**：定期检查 `/api/health` 和 `/api/queue` 状态

### 预设使用建议
- **优先使用预设**：大多数情况下预设即可满足需求
- **预设+微调**：需要特殊设置时，在预设基础上覆盖参数
- **完全自定义**：特殊场景下使用完整的应用列表

### 错误处理
所有API接口在出错时返回统一格式：
```json
{
  "status": "error",
  "message": "错误描述信息",
  "timestamp": "2025-07-04T10:30:00Z"
}
```

**常见错误码**：
- `400`: 请求参数错误
- `500`: 服务器内部错误

## 🔧 故障排除

### API服务无法启动
1. 检查端口9080是否被占用
2. 确认Flask依赖已正确安装：`pip install flask`
3. 查看程序启动日志

### 请求失败
1. 确认API服务正在运行
2. 检查请求格式和参数
3. 查看API日志文件：`logs/api_YYYY-MM-DD.log`

### 队列执行失败
1. 确认设备已连接：使用 `/api/devices` 检查
2. 检查队列参数是否正确
3. 查看主程序日志：`logs/app_YYYY-MM-DD.log`

### 预设不生效
1. 使用 `/api/presets` 确认预设名称正确
2. 检查预设名称的大小写和特殊字符
3. 查看API响应中的实际应用列表

## 📝 日志监控

### API日志
- **位置**：`logs/api_YYYY-MM-DD.log`
- **内容**：所有API请求、响应和错误信息
- **清理**：自动保留30天

### 主程序日志
- **位置**：`logs/app_YYYY-MM-DD.log`
- **内容**：程序运行状态和队列执行信息

## 🔒 安全注意事项

1. **本地访问**：API服务仅绑定到127.0.0.1，只能本地访问
2. **无身份验证**：当前版本不包含身份验证机制
3. **并发安全**：API操作与GUI操作互斥，确保数据一致性
4. **设备要求**：执行队列前需要确保设备已连接

## 📞 技术支持

如遇到问题，请：
1. 查看相关日志文件
2. 使用 `/api/health` 检查服务状态
3. 运行测试脚本：`python test_api.py`
4. 参考技术实现文档：`API_IMPLEMENTATION_SUMMARY.md`

---

**版本信息**：API v1.0.0
**更新日期**：2025-07-04
**兼容性**：Python 3.7+
