import subprocess

from utils.logs.LoggerManager import get_logger


class BaseAdb:
    def __init__(self):
        self.logger = get_logger(__name__)

    def get_device_list(self):
        """获取设备列表"""
        # 获取 adb devices 命令的输出
        output = self.user_run_adb_stdout("adb devices")

        # 初始化设备ID列表
        device_ids = []

        # 分割输出为行，并去除每行的首尾空白字符
        lines = output.strip().split('\n')

        # 遍历每一行，忽略第一行
        for line in lines[1:]:
            # 去除行的首尾空白字符，然后分割行内容
            parts = line.strip().split()
            if len(parts) >= 2 and parts[1] == 'device':
                # 去除设备ID的首尾空白字符并添加到列表中
                device_ids.append(parts[0].strip())

        return device_ids

    def on_home(self, device_id=None):
        """返回主界面"""
        if device_id:
            self.user_id_run_adb(dev_id=device_id, str='"input keyevent KEYCODE_HOME"')
            return
        self.user_run_adb("adb shell input keyevent KEYCODE_HOME")

    def start_auto_brightness(self):
        """打开自动亮度"""
        self.user_run_adb("adb shell settings put system screen_brightness_mode 1")

    def close_auto_brightness(self):
        """关闭自动亮度"""
        self.user_run_adb("adb shell settings put system screen_brightness_mode 0")

    def get_auto_brightness(self, device_id=None):
        """ 获取自动亮度状态 """
        if device_id:
            return self.user_id_run_adb_stdout(dev_id=device_id, str='"settings get system screen_brightness_mode"')
        return self.user_run_adb_stdout("adb shell settings get system screen_brightness_mode")

    def open_app(self, package_name: str, dev_id: str = None):
        """打开应用"""
        strs = "adb "
        if dev_id:
            strs += "-s " + dev_id + " "
        # 只通过包名打开
        text = strs + "shell monkey -p " + package_name + " -c android.intent.category.LAUNCHER 1"
        self.user_run_adb_no_log(text)

    def open_app_activity(self, package_name: str, activity_name: str, dev_id: str = None):
        """打开应用的activity"""
        strs = "adb "
        if dev_id:
            strs += "-s " + dev_id + " "
        text = strs + " shell am start -n " + package_name + "/" + activity_name
        self.user_run_adb(text)

    def user_run_adb_stdout(self, str):
        """运行命令，并捕获输出"""
        # 运行命令并捕获输出
        completed_process = subprocess.run(str, stdout=subprocess.PIPE, text=True, shell=True)
        self.logger.info(
            "\nuser_run_adb_stdout>>>运行命令，并捕获输出\n" +
            str +
            "\n" +
            "返回内容: " + completed_process.stdout
        )
        # 返回输出内容
        return completed_process.stdout

    def user_run_adb(self, strs):
        """用户自定义adb命令"""
        self.logger.info(
            "\nuser_run_adb>>>用户自定义adb命令\n" +
            strs
        )
        self.run_device_all(strs)

    def user_run_adb_no_log(self, strs):
        """ 执行命令但不输出信息 """
        for dev_id in self.get_device_list():
            self.logger.info(
                "\nuser_run_adb_no_log>>>执行命令但不输出信息\n" +
                strs.replace("adb", "adb -s " + str(dev_id) + " ")
            )
            subprocess.run(strs.replace("adb", "adb -s " + str(dev_id) + " "), stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, shell=True)

    def run_device_all(self, strs):
        """ 对所有设备执行命令 """
        for dev_id in self.get_device_list():
            self.logger.info(
                "\nrun_device_all>>>对所有设备执行命令\n" +
                strs.replace("adb", "adb -s " + str(dev_id) + " ")
            )
            subprocess.run(strs.replace("adb", "adb -s " + str(dev_id) + " "), shell=True)

    def user_id_run_adb(self, dev_id, str):
        """指定设备 运行命令"""
        self.logger.info(
            "\nuser_id_run_adb>>>指定设备 运行命令\n" +
            "adb -s " + dev_id + " shell " + str
        )
        subprocess.run("adb -s " + dev_id + " shell " + str, shell=True)

    def user_id_run_adb_stdout(self, dev_id, str):
        """ 指定设备 运行命令，并捕获输出"""
        self.logger.info(
            "\nuser_id_run_adb_stdout>>>指定设备 运行命令，并捕获输出\n" +
            "adb -s " + dev_id + " shell " + str
                         )
        completed_process = subprocess.run("adb -s " + dev_id + " shell " + str, stdout=subprocess.PIPE, text=True, shell=True)
        return completed_process.stdout.strip()
