"""
数据模型

定义了API响应数据的结构化模型，提供类型安全和便捷的数据访问。
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime


class HealthStatus:
    """健康状态模型"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化健康状态
        
        Args:
            data: API返回的健康状态数据
        """
        self.raw_data = data
        heartbeat = data.get("heartbeat", {})
        
        self.health = data.get("health", "unknown")
        self.status = heartbeat.get("status", "unknown")
        self.timestamp = heartbeat.get("timestamp")
        self.uptime = heartbeat.get("uptime", 0)
        self.queue_status = heartbeat.get("queue_status", "unknown")
        self.execution_status = heartbeat.get("execution_status", "unknown")
        self.device_connected = heartbeat.get("device_connected", False)
        self.has_errors = heartbeat.get("has_errors", False)
        self.api_requests_count = heartbeat.get("api_requests_count", 0)
    
    @property
    def is_healthy(self) -> bool:
        """检查服务是否健康"""
        return self.health == "healthy" and self.status == "alive"
    
    def __str__(self) -> str:
        return f"HealthStatus(health={self.health}, status={self.status}, device_connected={self.device_connected})"


class QueueInfo:
    """队列信息模型"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化队列信息
        
        Args:
            data: 队列数据
        """
        self.index = data.get("index", -1)
        self.title = data.get("title", "")
        self.type = data.get("type", "")
        self.apps = data.get("apps", [])
        self.is_auto_brightness = data.get("is_auto_brightness", False)
        self.status = data.get("status", "pending")
        self.is_current = data.get("is_current", False)
    
    def __str__(self) -> str:
        return f"QueueInfo(index={self.index}, title='{self.title}', status={self.status})"


class QueueStatus:
    """队列状态模型"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化队列状态
        
        Args:
            data: API返回的队列状态数据
        """
        self.raw_data = data
        queue_status = data.get("queue_status", {})
        
        self.queue_count = queue_status.get("queue_count", 0)
        self.is_running = queue_status.get("is_running", False)
        self.is_empty = queue_status.get("is_empty", True)
        
        # 解析队列列表
        self.queue_list = []
        for queue_data in data.get("queue_list", []):
            self.queue_list.append(QueueInfo(queue_data))
    
    @property
    def has_queues(self) -> bool:
        """检查是否有队列"""
        return self.queue_count > 0 and not self.is_empty
    
    def __str__(self) -> str:
        return f"QueueStatus(count={self.queue_count}, running={self.is_running}, empty={self.is_empty})"


class ExecutionSummary:
    """执行摘要模型"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化执行摘要
        
        Args:
            data: 执行摘要数据
        """
        self.execution_status = data.get("execution_status", "idle")
        self.total_queues = data.get("total_queues", 0)
        self.completed_queues = data.get("completed_queues", 0)
        self.failed_queues = data.get("failed_queues", 0)
        self.current_queue_index = data.get("current_queue_index", -1)
        self.is_running = data.get("is_running", False)
        self.start_time = data.get("start_time")
        self.duration_seconds = data.get("duration_seconds", 0)
        self.error_count = data.get("error_count", 0)
        # 使用API返回的is_completed字段，如果没有则回退到状态判断
        self._api_is_completed = data.get("is_completed")
    
    @property
    def is_completed(self) -> bool:
        """检查是否已完成（优先使用API返回的is_completed字段）"""
        # 优先使用API返回的is_completed字段
        if self._api_is_completed is not None:
            return self._api_is_completed

        # 回退到状态字符串判断
        return self.execution_status in ["completed", "failed", "stopped"]
    
    @property
    def progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_queues == 0:
            return 0.0
        return (self.completed_queues / self.total_queues) * 100
    
    def __str__(self) -> str:
        return f"ExecutionSummary(status={self.execution_status}, progress={self.completed_queues}/{self.total_queues})"


class ExecutionStatus:
    """执行状态模型"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化执行状态
        
        Args:
            data: API返回的执行状态数据
        """
        self.raw_data = data
        
        # 执行摘要
        summary_data = data.get("execution_summary", {})
        self.summary = ExecutionSummary(summary_data)
        
        # 详细队列信息
        self.detailed_queues = []
        for queue_data in data.get("detailed_queues", []):
            self.detailed_queues.append(QueueInfo(queue_data))
    
    def __str__(self) -> str:
        return f"ExecutionStatus({self.summary})"


class DeviceInfo:
    """设备信息模型"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化设备信息
        
        Args:
            data: API返回的设备数据
        """
        self.devices = data.get("devices", [])
        self.device_count = data.get("device_count", 0)
    
    @property
    def has_devices(self) -> bool:
        """检查是否有设备连接"""
        return self.device_count > 0
    
    def __str__(self) -> str:
        return f"DeviceInfo(count={self.device_count}, devices={self.devices})"


class ExecutionError:
    """执行错误模型"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化执行错误
        
        Args:
            data: 错误数据
        """
        self.timestamp = data.get("timestamp")
        self.queue_index = data.get("queue_index", -1)
        self.queue_title = data.get("queue_title", "")
        self.error_type = data.get("error_type", "")
        self.error_message = data.get("error_message", "")
    
    def __str__(self) -> str:
        return f"ExecutionError(type={self.error_type}, message='{self.error_message}')"
