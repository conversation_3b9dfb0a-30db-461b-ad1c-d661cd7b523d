# Author: <PERSON><PERSON>
# Datetime: 2021/9/16 
# Copyright belongs to the author.
# Please indicate the source for reprinting.

from typing import List, Dict, <PERSON><PERSON>
from collections import OrderedDict

import tkinter
from tkinter import ttk
from tkinter import filedialog

from qgui.manager import *
from qgui.base_tools import ConcurrencyModeFlag, check_callable, ArgInfo, select_var_dtype, BaseTool, make_anchor, \
    make_side

RUN_ICON = os.path.join(ICON_PATH, "play_w.png")

LEFT_PAD_LEN = 10
LABEL_WIDTH = 12
INPUT_BOX_LEN = 70
DEFAULT_PAD = 5


class BaseNotebookTool(BaseTool):
    """
    基础Notebook工具集，提供基础异步Callback
    1. 写Build，记得继承才会有self.master，继承时候传**kwargs
    2. 若需返回信息，请重写get_info方法->ArgInfo
    3. 如绑定func，需要封装Callback
    """

    def __init__(self,
                 bind_func=None,
                 name: str = None,
                 style: str = "primary",
                 tab_index: int = 0,
                 async_run: bool = False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         style=style,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode)
        self.tab_index = tab_index
        self.frame = frame


class BaseChooseFileTextButton(BaseNotebookTool):
    def __init__(self,
                 bind_func=None,
                 name: str = None,
                 label_info: str = "目标文件路径",
                 entry_info: str = "请选择文件路径",
                 button_info: str = "选 择 文 件 ",
                 style: str = "primary",
                 tab_index: int = 0,
                 async_run: bool = False,
                 mode="file",
                 frame: tkinter.Frame = None):
        super().__init__(bind_func, name=name, style=style, tab_index=tab_index, async_run=async_run, frame=frame)

        self.label_info = label_info
        self.button_info = button_info
        self.name = name
        self.mode = mode

        self.entry_var = tkinter.StringVar(value=entry_info)

    def build(self, **kwargs) -> tkinter.Frame:
        super().build(**kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=2)
        label = ttk.Label(frame,
                          text=self.label_info,
                          style="TLabel",
                          width=LABEL_WIDTH)
        label.pack(side="left")
        entry = ttk.Entry(frame,
                          style=self.style + "info.TEntry",
                          textvariable=self.entry_var)
        entry.pack(side="left", fill="x", expand="yes", padx=5, pady=2)

        if self.mode == "file":
            if not hasattr(self, "filetypes"):
                self.filetypes = [('All Files', '*')]

            def render():
                file_path = filedialog.askopenfilename(title="选择文件",
                                                       filetypes=self.filetypes)
                if file_path:
                    self.entry_var.set(file_path)

        else:
            def render():
                file_path = filedialog.askdirectory(title="选择文件夹")
                if file_path:
                    self.entry_var.set(file_path)

        command = self._callback(self.bind_func, start_func=render) if self.bind_func else render
        button = ttk.Button(frame,
                            text=self.button_info,
                            style=self.style + "TButton",
                            command=command,
                            width=12)
        button.pack(side="right")
        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo(name=field, set_func=self.entry_var.set, get_func=self.entry_var.get)

        return arg_info


class ChooseFileTextButton(BaseChooseFileTextButton):
    def __init__(self,
                 bind_func=None,
                 name: str = None,
                 label_info: str = "目标文件路径",
                 entry_info: str = "请选择文件路径",
                 button_info: str = "选 择 文 件",
                 filetypes: bool = None,
                 style: str = "primary",
                 tab_index: int = 0,
                 async_run: bool = False,
                 frame: tkinter.Frame = None):
        self.filetypes = [('All Files', '*')] if filetypes is None else filetypes

        super().__init__(bind_func=bind_func,
                         name=name,
                         label_info=label_info,
                         entry_info=entry_info,
                         button_info=button_info,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         frame=frame)


class ChooseDirTextButton(BaseChooseFileTextButton):
    def __init__(self,
                 bind_func=None,
                 name=None,
                 label_info: str = "目标文件夹路径",
                 entry_info: str = "请选择文件夹路径",
                 button_info: str = "选择文件夹",
                 style: str = "primary",
                 tab_index: int = 0,
                 async_run: bool = False,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         label_info=label_info,
                         entry_info=entry_info,
                         button_info=button_info,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         mode="dir",
                         frame=frame)


class BaseButton(BaseNotebookTool):
    def __init__(self,
                 bind_func,
                 name: str = None,
                 text: str = "开始执行",
                 icon: str = None,
                 checked_text: str = None,
                 async_run: bool = True,
                 style: str = "primary",
                 tab_index: int = 0,
                 concurrency_mode: bool = False,
                 side: str = RIGHT,
                 add_width=8,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func,
                         name=name,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)
        self.text = text
        self.checked_text = checked_text
        self.add_width = add_width
        self.side = side

        self.icon = icon

    def build(self, **kwargs) -> tkinter.Frame:
        super().build(**kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=5)
        if self.icon:
            self.icon = tkinter.PhotoImage(file=self.icon)
        else:
            self.icon = None

        self.text_var = tkinter.StringVar(frame, value=self.text)

        def click_btn():
            self.btn.configure(style=self.style + "TButton")
            self.btn.configure(state="disable")
            if self.checked_text:
                self.text_var.set(self.checked_text)

        def done_btn():
            self.btn.configure(style=self.style + "TButton")
            self.btn.configure(state="normal")
            self.text_var.set(self.text)

        if not self.bind_func:
            # 不知道为啥必须要有，不然文字不会显示，会头Debug一下
            self.bind_func = lambda x: None
        self.btn = ttk.Button(frame,
                              textvariable=self.text_var,
                              image=self.icon,
                              width=len(self.text) + self.add_width,
                              compound='left',
                              command=self._callback(self.bind_func, click_btn, done_btn),
                              style=self.style + "TButton")

        self.btn.pack(side=make_side(self.side), padx=5, pady=5)
        return frame


class RunButton(BaseButton):
    def __init__(self,
                 bind_func,
                 name: str = None,
                 text: str = "开始执行",
                 checked_text: str = "正在执行",
                 async_run: bool = True,
                 style: str = "success",
                 tab_index: int = 0,
                 concurrency_mode: bool = False,
                 side: str = RIGHT,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         text=text,
                         checked_text=checked_text,
                         async_run=async_run,
                         style=style,
                         tab_index=tab_index,
                         concurrency_mode=concurrency_mode,
                         add_width=6,
                         icon=RUN_ICON,
                         side=side,
                         frame=frame)


class InputBox(BaseNotebookTool):
    def __init__(self,
                 name: str = None,
                 default: str = "请在此输入",
                 label_info: str = "输入信息",
                 style: str = "primary",
                 tab_index=0,
                 frame: tkinter.Frame = None):
        super().__init__(name=name,
                         style=style,
                         tab_index=tab_index,
                         frame=frame)
        self.input_vars = tkinter.StringVar(value=default)
        self.label_info = label_info

    def build(self, **kwargs):
        super().build(**kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=5)
        label = ttk.Label(frame,
                          text=self.label_info,
                          style="TLabel",
                          width=LABEL_WIDTH)
        label.pack(side="left")

        entry = ttk.Entry(frame,
                          style=self.style + "info.TEntry",
                          textvariable=self.input_vars,
                          width=INPUT_BOX_LEN)
        entry.pack(side="left", fill="x", padx=5, pady=2)
        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo(name=field, set_func=self.input_vars.set, get_func=self.input_vars.get)

        return arg_info


class Combobox(BaseNotebookTool):
    def __init__(self,
                 bind_func=None,
                 name=None,
                 title: str = "请下拉选择",
                 options: List[str] = None,
                 style="custom",
                 tab_index=0,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         style=style,
                         tab_index=tab_index,
                         frame=frame)
        self.title = title
        self.options = options

        self.options = options if options else ["--请选择--"]

    def build(self, **kwargs):
        super().build(**kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=5)
        label = ttk.Label(frame,
                          text=self.title,
                          style="TLabel",
                          width=LABEL_WIDTH)
        label.pack(side="left")
        self.comb = ttk.Combobox(frame,
                                 style=self.style + "TCombobox",
                                 values=self.options)
        self.comb.current(0)
        if self.bind_func:
            self.comb.bind('<<ComboboxSelected>>', self._callback(self.bind_func))
        self.comb.pack(side="left", padx=5, pady=2)

        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo(name=field, set_func=self.comb.set, get_func=self.comb.get)

        return arg_info


class Slider(BaseNotebookTool):
    def __init__(self,
                 name=None,
                 title: str = "请拖动滑块",
                 default: int = 0,
                 min_size: int = 0,
                 max_size: int = 100,
                 dtype=int,
                 style: str = "primary",
                 tab_index: int = 0,
                 frame: tkinter.Frame = None):
        super().__init__(name=name,
                         style=style,
                         tab_index=tab_index,
                         frame=frame)
        self.title = title
        self.default = default
        self.min_size = min_size
        self.max_size = max_size
        self.dtype = dtype

    def slider_var_trace(self, *args):
        v = self.scale.get()
        self.value_var.set(f"当前值 {self.dtype(v)}")

    def build(self, **kwargs):
        super().build(**kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=5)

        self.slider_var = select_var_dtype(self.dtype)(frame, value=self.default)
        self.value_var = tkinter.StringVar(frame, value=f"当前值 {self.default}")
        self.slider_var.trace("w", self.slider_var_trace)

        label = ttk.Label(frame,
                          text=self.title,
                          style="TLabel",
                          width=LABEL_WIDTH)
        label.pack(side="left")
        self.scale = ttk.Scale(frame,
                               from_=self.min_size,
                               to=self.max_size,
                               value=self.default,
                               variable=self.slider_var)
        # ToDo ttk 的Bug
        # self.scale.configure(style="info.TSlider")
        self.scale.pack(side="left", padx=5, fill="x", expand="yes")
        self.value = ttk.Label(frame,
                               textvariable=self.value_var,
                               style="TLabel",
                               width=LABEL_WIDTH)
        self.value.pack(side="right")
        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo(name=field, set_func=self.scale.set, get_func=self.scale.get)

        return arg_info


class BaseCheckButton(BaseNotebookTool):
    def __init__(self,
                 options: str or Tuple[str, bool] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 button_style="TCheckbutton",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 mode=None,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)
        self.title = title
        self.mode = mode
        if isinstance(options, str):
            self.options = {options: 0}
        if isinstance(options, tuple):
            self.options = {options[0]: 1 if options[1] else 0}
        if isinstance(options, list):
            self.options = OrderedDict()
            if len(options[0]) != 2:
                raise TypeError(f"{self.__class__.__name__}的options参数需要为str or List[Tuple[str, bool]]格式\n"
                                f"Example:\n"
                                f"'选择框1' or [('选择1', 0), ('选择2', 1), ('选择3', 0)]")
            for option in options:
                self.options[option[0]] = 1 if option[1] else 0
        self.button_style = button_style

    def build(self, *args, **kwargs):
        super().build(*args, **kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=5)
        label = ttk.Label(frame,
                          text=self.title,
                          style="TLabel",
                          width=LABEL_WIDTH)
        label.pack(side="left")

        self.value_vars = dict()
        for option in self.options:
            self.value_vars[option] = tkinter.StringVar(frame, value=self.options[option])
            if self.mode == "ToolButton":
                pad_x = 0
            else:
                pad_x = 5
            ttk.Checkbutton(frame,
                            text=option,
                            style=self.style + self.button_style,
                            variable=self.value_vars[option],
                            command=self._callback(self.bind_func)).pack(side="left", padx=pad_x)
        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo()
        for v in self.value_vars:
            arg_info += ArgInfo(name=field + "-" + v, set_func=self.value_vars[v].set, get_func=self.value_vars[v].get)

        return arg_info

    def set_status(self, status, name):
        for option in self.value_vars:
            if name in option:
                self.value_vars[option].set(status)


class TaskQueueDisplay(BaseNotebookTool):
    def __init__(self,
                 title="任务队列",
                 name=None,
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 frame=None,
                 button_style="Outline.Toolbutton",
                 max_width=300,
                 bind_func=None):
        super().__init__(name=name, style=style, tab_index=tab_index, async_run=async_run, frame=frame, bind_func=bind_func)
        self.title = title
        self.tasks = OrderedDict()
        self.button_style = button_style
        self.max_width = max_width  # 固定宽度
        self.max_height = 1  # 固定高度

    def build(self, *args, **kwargs):
        super().build(*args, **kwargs)

        if self.frame:
            container = self.frame
        else:
            container = ttk.Frame(self.master)
            container.pack(side="top", fill="both", expand=True)

        if self.title:
            label = ttk.Label(container, text=self.title)
            label.pack(side="top", fill="x")

        # 创建固定大小的外部框架
        self.outer_frame = ttk.Frame(container)
        self.outer_frame.pack(side="top", fill="both", expand=True)
        self.outer_frame.pack_propagate(False)  # 禁止自动调整大小
        
        # 创建Canvas和Scrollbar
        self.canvas = tkinter.Canvas(self.outer_frame)
        self.scrollbar = ttk.Scrollbar(self.outer_frame, orient="vertical", command=self.canvas.yview)
        
        # 配置Canvas和Scrollbar
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # 创建内部Frame来放置任务按钮
        self.inner_frame = ttk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window((0, 0), window=self.inner_frame, anchor="nw")
        
        # 绑定事件
        self.inner_frame.bind("<Configure>", self._on_frame_configure)
        self.canvas.bind("<Configure>", self._on_canvas_configure)
        
        # 创建任务区域
        self.content_frame = ttk.Frame(self.inner_frame)
        self.content_frame.pack(side="top", fill="x")
        
        # 初始化任务行容器
        self.current_row = ttk.Frame(self.content_frame)
        self.current_row.pack(side="top", fill="x", pady=2)
        self.rows = [self.current_row]
        self.current_width = 0

        return container
    
    def _on_frame_configure(self, event=None):
        # 更新Canvas的滚动区域
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def _on_canvas_configure(self, event=None):
        # 当Canvas大小改变时，调整内部框架的宽度
        if event:
            self.canvas.itemconfig(self.canvas_window, width=event.width)

    def add_task(self, task_name):
        # 如果任务已存在，直接返回，不重复添加
        if task_name in self.tasks:
            return
            
        var = tkinter.BooleanVar(value=False)
        try:
            # 创建按钮
            checkbutton = ttk.Checkbutton(self.current_row, text=task_name, variable=var,
                                      style=self.style + self.button_style,
                                      command=lambda task=task_name: self.remove_task(task))
            
            # 估算按钮宽度
            estimated_width = len(task_name) * 10 + 40
            
            # 如果当前行宽度加上新按钮宽度超过最大宽度，创建新行
            if self.current_width + estimated_width > self.max_width - 30:  # 留一些边距
                self.current_row = ttk.Frame(self.content_frame)
                self.current_row.pack(side="top", fill="x", pady=2)
                self.rows.append(self.current_row)
                self.current_width = 0
                
                # 重新创建按钮在新行
                checkbutton.destroy()
                checkbutton = ttk.Checkbutton(self.current_row, text=task_name, variable=var,
                                          style=self.style + self.button_style,
                                          command=lambda task=task_name: self.remove_task(task))
            
            # 添加按钮到当前行
            checkbutton.pack(side="left", padx=5, pady=2)
            
            # 更新当前行宽度
            self.current_width += estimated_width
            
            # 更新Canvas的滚动区域
            self._on_frame_configure()
            
            # 保存任务
            self.tasks[task_name] = {"var": var, "button": checkbutton, "row": self.current_row}
        except Exception as e:
            print(f"添加任务 {task_name} 失败: {e}")

    def remove_task(self, task_name):
        try:
            if task_name in self.tasks:
                task_info = self.tasks[task_name]
                button = task_info["button"]
                row = task_info["row"]
                
                # 删除按钮
                button.destroy()
                
                # 从任务字典中移除
                del self.tasks[task_name]
                
                # 检查行是否为空
                if not row.winfo_children():
                    # 如果行为空且不是唯一的行，删除它
                    if len(self.rows) > 1 and row in self.rows:
                        self.rows.remove(row)
                        row.destroy()
                        
                        # 如果删除的是当前行，更新当前行
                        if row == self.current_row and self.rows:
                            self.current_row = self.rows[-1]
                            
                            # 重新计算当前行宽度
                            self.current_width = sum(child.winfo_reqwidth() for child in self.current_row.winfo_children())
                
                # 更新Canvas的滚动区域
                self._on_frame_configure()
            
            if self.bind_func:
                return self.bind_func(task_name)
        except Exception as e:
            print(f"移除任务 {task_name} 失败: {e}")

    def clear_all_tasks(self):
        try:
            # 清除所有任务
            for task_name, task_info in list(self.tasks.items()):
                button = task_info["button"]
                button.destroy()
            
            # 清除所有行，除了第一行
            for row in list(self.rows)[1:]:
                row.destroy()
            
            # 保留第一行
            if self.rows:
                self.current_row = self.rows[0]
                self.rows = [self.current_row]
            else:
                # 如果没有行，创建一个
                self.current_row = ttk.Frame(self.content_frame)
                self.current_row.pack(side="top", fill="x", pady=2)
                self.rows = [self.current_row]
            
            # 重置当前行宽度
            self.current_width = 0
            
            # 清空任务字典
            self.tasks.clear()
            
            # 更新Canvas的滚动区域
            self._on_frame_configure()

            # 如果有绑定函数需要在清空后调用，则调用它
            if self.bind_func:
                self.bind_func(None)
        except Exception as e:
            print(f"清空所有任务时出错: {e}")

    def get_all_task_names(self):
        """
           获取当前所有按钮的文字。

           :return: 包含所有任务名称的列表。
           """
        return list(self.tasks.keys())

    def get_arg_info(self):
        arg_info = ArgInfo(name=self.name if self.name else "TaskQueueDisplay")
        for task_name, task_info in self.tasks.items():
            arg_info += ArgInfo(name=f"{self.name}-{task_name}", get_func=task_info["var"].get)
        return arg_info


# 自定义 TaskQueueDisplay 类，支持更大的高度和滚动
class CustomTaskQueueDisplay(TaskQueueDisplay):
    def __init__(self,
                 title="任务队列",
                 name=None,
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 frame=None,
                 button_style="Outline.Toolbutton",
                 max_width=300,
                 bind_func=None):
        # 初始化基类属性
        super().__init__(title=title, name=name, style=style, tab_index=tab_index, 
                         async_run=async_run, frame=frame, button_style=button_style, 
                         max_width=max_width, bind_func=bind_func)
        # 设置更大的高度值，使滚动条可见
        self.max_height = 400  # 像素单位
        # 添加选中项索引属性
        self.selected_index = -1
        # 添加队列项目列表
        self.queue_items = []
        # 初始化行属性
        self.rows = []
        self.current_row = None
        self.current_width = 0

    def build(self, *args, **kwargs):
        # 直接调用父类的build方法来确保UI初始化正确
        return super().build(*args, **kwargs)
    
    def _scrollbar_set(self, first, last):
        """自定义滚动条设置函数，只有当内容超出视图时才显示滚动条"""
        self.scrollbar.set(first, last)
        
        # 检查内容是否超出视图
        if float(first) <= 0.0 and float(last) >= 1.0:
            # 内容未超出视图，隐藏滚动条
            self.scrollbar.pack_forget()
        else:
            # 内容超出视图，显示滚动条
            if not self.scrollbar.winfo_manager():
                self.scrollbar.pack(side="right", fill="y")
    
    def _bind_mousewheel(self, widget):
        """绑定鼠标滚轮事件到组件，避免重复递归绑定"""
        widget.bind("<MouseWheel>", self._on_mousewheel)  # Windows
        widget.bind("<Button-4>", self._on_mousewheel)    # Linux 上滚
        widget.bind("<Button-5>", self._on_mousewheel)    # Linux 下滚
        
        # 对所有子组件也进行绑定
        for child in widget.winfo_children():
            self._bind_mousewheel(child)
    
    def _on_mousewheel(self, event):
        # 获取滚动条的当前位置
        first, last = map(float, self.scrollbar.get())
        
        # 如果内容未超出视图，不进行滚动
        if first <= 0.0 and last >= 1.0:
            return
        
        # 处理鼠标滚轮事件
        if hasattr(event, 'num') and event.num == 4 or (hasattr(event, 'delta') and event.delta > 0):
            self.canvas.yview_scroll(-1, "units")  # 向上滚动
        elif hasattr(event, 'num') and event.num == 5 or (hasattr(event, 'delta') and event.delta < 0):
            self.canvas.yview_scroll(1, "units")   # 向下滚动
        
        # 阻止事件继续传播
        return "break"
    
    def _on_frame_configure(self, event=None):
        # 更新Canvas的滚动区域
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
        # 获取内容的实际高度
        content_height = self.inner_frame.winfo_reqheight()
        canvas_height = self.canvas.winfo_height()
        
        # 调整Canvas的高度，但不超过最大高度
        if content_height < self.max_height:
            # 内容高度小于最大高度，使用内容的实际高度
            self.outer_frame.config(height=content_height)
        else:
            # 内容高度大于最大高度，使用最大高度
            self.outer_frame.config(height=self.max_height)
    
    def add_task(self, task_name):
        """重写添加任务方法，确保新添加的按钮也绑定滚轮事件"""
        # 如果任务已存在，直接返回，不重复添加
        if task_name in self.tasks:
            return
            
        var = tkinter.BooleanVar(value=False)
        try:
            # 创建按钮
            checkbutton = ttk.Checkbutton(self.current_row, text=task_name, variable=var,
                                      style=self.style + self.button_style,
                                      command=lambda task=task_name: self.remove_task(task))
            
            # 估算按钮宽度
            estimated_width = len(task_name) * 10 + 40
            
            # 如果当前行宽度加上新按钮宽度超过最大宽度，创建新行
            if self.current_width + estimated_width > self.max_width - 30:  # 留一些边距
                self.current_row = ttk.Frame(self.content_frame)
                self.current_row.pack(side="top", fill="x", pady=2)
                self.rows.append(self.current_row)
                self.current_width = 0
                
                # 绑定滚轮事件到新行
                self._bind_mousewheel(self.current_row)
                
                # 重新创建按钮在新行
                checkbutton.destroy()
                checkbutton = ttk.Checkbutton(self.current_row, text=task_name, variable=var,
                                          style=self.style + self.button_style,
                                          command=lambda task=task_name: self.remove_task(task))
            
            # 添加按钮到当前行
            checkbutton.pack(side="left", padx=5, pady=2)
            
            # 绑定滚轮事件到按钮
            self._bind_mousewheel(checkbutton)
            
            # 更新当前行宽度
            self.current_width += estimated_width
            
            # 更新Canvas的滚动区域
            self._on_frame_configure()
            
            # 保存任务
            self.tasks[task_name] = {"var": var, "button": checkbutton, "row": self.current_row}
        except Exception as e:
            print(f"添加任务 {task_name} 失败: {e}")
    
    def remove_task(self, task_name):
        """重写移除任务方法，确保正确更新滚动区域"""
        result = super().remove_task(task_name)
        # 确保滚动区域更新
        self._on_frame_configure()
        return result
    
    def add_item(self, item_title):
        """添加队列项目"""
        try:
            # 将项目添加到队列
            self.queue_items.append(item_title)
            # 调用添加任务的方法（如果UI已初始化）
            if hasattr(self, 'content_frame') and self.content_frame:
                self.add_task(item_title)
        except Exception as e:
            print(f"添加队列项目失败: {e}")
            
    def select_item(self, index):
        """选中指定索引的队列项目"""
        try:
            # 如果有之前选中的项目，取消选中状态
            if self.selected_index >= 0 and self.selected_index < len(self.queue_items):
                old_title = self.queue_items[self.selected_index]
                if old_title in self.tasks:
                    button = self.tasks[old_title]["button"]
                    button.state(['!selected'])
            
            # 设置新的选中项
            self.selected_index = index
            if index >= 0 and index < len(self.queue_items):
                title = self.queue_items[index]
                if title in self.tasks:
                    button = self.tasks[title]["button"]
                    button.state(['selected'])
        except Exception as e:
            print(f"选中队列项目失败: {e}")
            
    def unselect_all(self):
        """取消所有选中状态"""
        try:
            # 重置选中索引
            self.selected_index = -1
            
            # 取消所有按钮的选中状态
            for task_name, task_info in self.tasks.items():
                button = task_info["button"]
                button.state(['!selected'])
        except Exception as e:
            print(f"取消队列项目选中状态失败: {e}")
            
    def clear_all_tasks(self):
        """清空所有任务，并重置内部行/宽度及队列信息，避免多次添加时组件高度不断累积"""
        # 先调用父类的清理逻辑，完整复位按钮行、行列表以及宽度等状态
        super().clear_all_tasks()

        # 额外清理自定义扩展的队列信息
        self.queue_items.clear()
        self.selected_index = -1


class BaseCheckButtonAutoLine(BaseNotebookTool):
    """ 超出宽度自动换行 """
    def __init__(self,
                 options: str or Tuple[str, bool] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 button_style="TCheckbutton",
                 tab_index=0,
                 async_run=False,
                 line_width=500,
                 disabled=False,
                 max_height=200,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 mode=None,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)
        self.title = title
        self.mode = mode
        self.line_width = line_width
        self.disabled = disabled
        self.max_height = max_height
        if isinstance(options, str):
            self.options = {options: 0}
        if isinstance(options, tuple):
            self.options = {options[0]: 1 if options[1] else 0}
        if isinstance(options, list):
            self.options = OrderedDict()
            if len(options[0]) != 2:
                raise TypeError(f"{self.__class__.__name__}的options参数需要为str or List[Tuple[str, bool]]格式\n"
                                f"Example:\n"
                                f"'选择框1' or [('选择1', 0), ('选择2', 1), ('选择3', 0)]")
            for option in options:
                self.options[option[0]] = 1 if option[1] else 0
        self.button_style = button_style

    def build(self, *args, **kwargs):
        """构建带有垂直滚动条的多选按钮组件，超出固定高度自动出现滚动条"""
        # 只执行 BaseNotebookTool.build，避免触发父类旧的布局逻辑
        BaseNotebookTool.build(self, *args, **kwargs)

        # 创建外部固定尺寸框架
        if self.frame:
            outer_frame = self.frame
        else:
            outer_frame = ttk.Frame(self.master, style="TFrame")
            outer_frame.pack(side="top", fill="x", padx=5, pady=5)

        # 固定高度，禁止自动伸缩
        outer_frame.config(height=self.max_height)
        outer_frame.pack_propagate(False)

        # 创建 Canvas 与滚动条
        self.canvas = tkinter.Canvas(outer_frame)
        self.scrollbar = ttk.Scrollbar(outer_frame, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # 创建内部容器，用于放置实际内容
        self.inner_frame = ttk.Frame(self.canvas, style="TFrame")
        self.canvas_window = self.canvas.create_window((0, 0), window=self.inner_frame, anchor="nw")

        # 事件绑定以更新滚动区域及处理鼠标滚轮
        self.inner_frame.bind("<Configure>", self._on_frame_configure)
        self.canvas.bind("<Configure>", self._on_canvas_configure)
        self._bind_mousewheel(self.inner_frame)

        # ---- 以下与原父类 build 中一致 ----
        container = self.inner_frame

        if self.title:
            label = ttk.Label(container,
                              text=self.title,
                              style="TLabel",
                              width=LABEL_WIDTH)
            label.pack(side="left")

        self.value_vars = dict()
        current_row_frame = ttk.Frame(container)  # 第一行容器
        current_row_frame.pack(side="top", fill="x", padx=5, pady=5)

        container.update_idletasks()

        max_width = self.line_width
        current_width = 0
        pad_x = 5

        for option in self.options:
            self.value_vars[option] = tkinter.StringVar(value=self.options[option])
            checkbutton = ttk.Checkbutton(current_row_frame,
                                          text=option,
                                          style=self.style + self.button_style,
                                          variable=self.value_vars[option],
                                          command=lambda opt=option, var=self.value_vars[option]: self._toggle_entry(opt, var.get() == "1"))
            checkbutton.pack(side="left", padx=pad_x)

            # 如果禁用，设置状态
            if getattr(self, "disabled", False):
                checkbutton.configure(state="disabled")

            # 估算按钮宽度，避免频繁 update_idletasks 造成卡顿
            button_width = len(option) * 10 + 40  # 粗略估算

            if current_width + button_width > max_width:
                # 新建一行
                current_row_frame = ttk.Frame(container)
                current_row_frame.pack(side="top", fill="x", padx=5, pady=5)
                current_width = 0

                # 重新打包按钮到新行
                checkbutton.pack_forget()
                checkbutton.pack(side="left", padx=pad_x)

            current_width += button_width + pad_x

        return outer_frame

    # -----------------  辅助方法 -----------------
    def _on_frame_configure(self, event=None):
        """更新 Canvas 的滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _on_canvas_configure(self, event=None):
        """保持内部 Frame 宽度与 Canvas 同步"""
        if event:
            self.canvas.itemconfig(self.canvas_window, width=event.width)

    def _bind_mousewheel(self, widget):
        """给所有子组件绑定鼠标滚轮"""
        widget.bind("<MouseWheel>", self._on_mousewheel)  # Windows
        widget.bind("<Button-4>", self._on_mousewheel)    # Linux 上滚
        widget.bind("<Button-5>", self._on_mousewheel)    # Linux 下滚
        for child in widget.winfo_children():
            self._bind_mousewheel(child)

    def _on_mousewheel(self, event):
        if (hasattr(event, 'num') and event.num == 4) or (hasattr(event, 'delta') and event.delta > 0):
            self.canvas.yview_scroll(-1, "units")
        elif (hasattr(event, 'num') and event.num == 5) or (hasattr(event, 'delta') and event.delta < 0):
            self.canvas.yview_scroll(1, "units")
        return "break"

    def _toggle_entry(self, option, var):
        if self.bind_func:
            self.bind_func((option, var))

    def update_box(self, option, is_selected):
        """
                更新指定多选按钮的选中状态。

                :param option: 要更新状态的按钮文本。
                :param is_selected: 布尔值，表示是否选中按钮。
                """
        # 检查该选项是否存在
        if option in self.value_vars:
            var = self.value_vars[option]
            var.set('1' if is_selected else '0')  # 根据 is_selected 更新按钮的选中状态

    def update_box_all(self, is_selected):
        """
        更新所有多选按钮的选中状态。

        :param is_selected: 布尔值，True 表示选中所有按钮，False 表示取消选中所有按钮。
        """
        for var in self.value_vars.values():
            var.set('1' if is_selected else '0')


def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo()
        for v in self.value_vars:
            arg_info += ArgInfo(name=field + "-" + v, set_func=self.value_vars[v].set, get_func=self.value_vars[v].get)
        return arg_info


class BaseCheckButtonInput(BaseNotebookTool):
    """ 多选按钮 + 输入框 """
    def __init__(self,
                 options: str or Tuple[str, bool] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 button_style="TCheckbutton",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 mode=None,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)
        self.title = title
        self.mode = mode
        if isinstance(options, str):
            self.options = {options: 0}
        if isinstance(options, tuple):
            self.options = {options[0]: 1 if options[1] else 0}
        if isinstance(options, list):
            self.options = OrderedDict()
            if len(options[0]) != 2:
                raise TypeError(f"{self.__class__.__name__}的options参数需要为str or List[Tuple[str, bool]]格式\n"
                                f"Example:\n"
                                f"'选择框1' or [('选择1', 0), ('选择2', 1), ('选择3', 0)]")
            for option in options:
                self.options[option[0]] = 1 if option[1] else 0
        self.button_style = button_style

    def build(self, *args, **kwargs):
        super().build(*args, **kwargs)

        if self.frame:
            self.main_frame = self.frame
        else:
            self.main_frame = ttk.Frame(self.master, style="TFrame")
            self.main_frame.pack(side="top", fill="x", padx=5, pady=5)

        if self.title:
            label = ttk.Label(self.main_frame,
                              text=self.title,
                              style="TLabel",
                              width=LABEL_WIDTH)
            label.pack(side="left")

        # 初始化第一行
        self.current_row_frame = ttk.Frame(self.main_frame)
        self.current_row_frame.pack(fill='x', expand=True)
        self.current_width = 0
        # 获取主框架宽度作为最大宽度
        self.max_width = 800

        self.value_vars = dict()
        self.entry_widgets = dict()
        for option in self.options:
            # 创建一个新行 Frame 用于放置 Checkbutton 和 Entry
            row_frame = ttk.Frame(self.main_frame)
            row_frame.pack(fill='x', expand=True)

            value_var = tkinter.StringVar(value=self.options[option])
            check_button = ttk.Checkbutton(
                row_frame,
                text=option,
                style=self.style + self.button_style,
                variable=value_var,
                command=lambda opt=option: self.toggle_entry_state(opt)
            )
            check_button.pack(side='left', padx=5)

            entry_var = tkinter.StringVar()
            entry = ttk.Entry(row_frame, textvariable=entry_var, state='disabled')
            entry.pack(side='left', padx=5)

            self.value_vars[option] = value_var
            self.entry_widgets[option] = entry

        return self.main_frame

    def toggle_entry_state(self, option):
        if self.value_vars[option].get() == '1':
            self.entry_widgets[option]['state'] = 'normal'  # 如果对应的选项被选中，则启用输入框
        else:
            self.entry_widgets[option]['state'] = 'disabled'  # 如果对应的选项未被选中，则禁用输入框

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo()
        for option in self.options:
            arg_info += ArgInfo(name=field + "-" + option,
                                set_func=lambda value, opt=option: self.value_vars[opt].set(value),
                                get_func=lambda opt=option: self.value_vars[opt].get())
        return arg_info


class CheckButton(BaseCheckButton):
    def __init__(self,
                 options: str or Tuple[str] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="TCheckbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)


class CheckToolButton(BaseCheckButton):
    def __init__(self,
                 options: str or Tuple[str] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="info",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         mode="ToolButton",
                         frame=frame)


class CheckObviousToolButton(BaseCheckButton):
    def __init__(self,
                 options: str or Tuple[str] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Outline.Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         mode="ToolButton",
                         frame=frame)


class CheckObviousToolButtonAutoLine(BaseCheckButtonAutoLine):
    def __init__(self,
                 options: str or Tuple[str] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 line_width=500,
                 disabled=False,
                 max_height=280,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Outline.Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         line_width=line_width,
                         disabled=disabled,
                         max_height=max_height,
                         concurrency_mode=concurrency_mode,
                         mode="ToolButton",
                         frame=frame)


class CheckObviousToolButtonInput(BaseCheckButtonInput):
    def __init__(self,
                 options: str or Tuple[str] or List[Tuple[str, bool]],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Outline.Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         mode="ToolButton",
                         frame=frame)


class ToggleButton(BaseCheckButton):
    def __init__(self,
                 options: str or Tuple[str],
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        assert not isinstance(options, list), "开关按钮仅有开和关两个选项，请传入单个选项"
        super().__init__(options=options,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Roundtoggle.Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)


class BaseRadioButton(BaseNotebookTool):
    def __init__(self,
                 options: str or List[str],
                 default: str = None,
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 button_style="TRadiobutton",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 mode=None,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)
        self.title = title
        self.mode = mode
        self.options = [options] if isinstance(options, str) else options
        self.default = default if default else options[0]
        self.button_style = button_style

    def build(self, *args, **kwargs):
        super().build(*args, **kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=5)

        if self.title:
            label = ttk.Label(frame,
                              text=self.title,
                              style="TLabel",
                              width=LABEL_WIDTH)
            label.pack(side="left")

        self.value_var = tkinter.StringVar(frame, value=self.options[0])
        for option in self.options:
            if self.mode == "ToolButton":
                pad_x = 0
            else:
                pad_x = 5
            ttk.Radiobutton(frame,
                            text=option,
                            style=self.style + self.button_style,
                            variable=self.value_var,
                            value=option,
                            command=self._callback(self.bind_func)).pack(side="left", padx=pad_x)
        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo(name=field, set_func=self.value_var.set, get_func=self.value_var.get)

        return arg_info


class RadioButton(BaseRadioButton):
    def __init__(self,
                 options: str or List[str],
                 default: str = None,
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         default=default,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="TRadiobutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         mode=None,
                         frame=frame)


class RadioToolButton(BaseRadioButton):
    def __init__(self,
                 options: str or List[str],
                 default: str = None,
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="info",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         default=default,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         mode="ToolButton",
                         frame=frame)


class RadioObviousToolButton(BaseRadioButton):
    def __init__(self,
                 options: str or List[str],
                 default: str = None,
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         default=default,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Outline.Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         mode="ToolButton",
                         frame=frame)


class BaseRadioButtonAutoLine(BaseNotebookTool):
    """ 超出宽度自动换行的单选按钮组 """
    def __init__(self,
                 options: str or List[str],
                 default: str = None,
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 button_style="TRadiobutton",
                 tab_index=0,
                 async_run=False,
                 line_width=500,
                 max_height=200,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 mode=None,
                 frame: tkinter.Frame = None):
        super().__init__(bind_func=bind_func,
                         name=name,
                         style=style,
                         tab_index=tab_index,
                         async_run=async_run,
                         concurrency_mode=concurrency_mode,
                         frame=frame)
        self.title = title
        self.mode = mode
        self.line_width = line_width
        self.max_height = max_height
        self.options = [options] if isinstance(options, str) else options
        self.default = default if default else self.options[0]
        self.button_style = button_style

    def build(self, *args, **kwargs):
        """构建带有垂直滚动条的单选按钮组件，超出固定高度自动出现滚动条"""
        # 只执行 BaseNotebookTool.build，避免触发父类旧的布局逻辑
        BaseNotebookTool.build(self, *args, **kwargs)

        # 创建外部固定尺寸框架
        if self.frame:
            outer_frame = self.frame
        else:
            outer_frame = ttk.Frame(self.master, style="TFrame")
            outer_frame.pack(side="top", fill="x", padx=5, pady=5)

        # 固定高度，禁止自动伸缩
        outer_frame.config(height=self.max_height)
        outer_frame.pack_propagate(False)

        # 创建 Canvas 与滚动条
        self.canvas = tkinter.Canvas(outer_frame)
        self.scrollbar = ttk.Scrollbar(outer_frame, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # 创建内部容器，用于放置实际内容
        self.inner_frame = ttk.Frame(self.canvas, style="TFrame")
        self.canvas_window = self.canvas.create_window((0, 0), window=self.inner_frame, anchor="nw")

        # 事件绑定以更新滚动区域及处理鼠标滚轮
        self.inner_frame.bind("<Configure>", self._on_frame_configure)
        self.canvas.bind("<Configure>", self._on_canvas_configure)
        self._bind_mousewheel(self.inner_frame)

        # ---- 以下是单选按钮的构建逻辑 ----
        container = self.inner_frame

        if self.title:
            label = ttk.Label(container,
                              text=self.title,
                              style="TLabel",
                              width=LABEL_WIDTH)
            label.pack(side="left")

        self.value_var = tkinter.StringVar(container, value=self.default)
        
        current_row_frame = ttk.Frame(container)  # 第一行容器
        current_row_frame.pack(side="top", fill="x", padx=5, pady=5)

        container.update_idletasks()

        max_width = self.line_width
        current_width = 0
        pad_x = 5

        for option in self.options:
            if self.mode == "ToolButton":
                pad_x = 0
            else:
                pad_x = 5
                
            radiobutton = ttk.Radiobutton(current_row_frame,
                           text=option,
                           style=self.style + self.button_style,
                           variable=self.value_var,
                           value=option,
                           command=self._callback(self.bind_func))
            radiobutton.pack(side="left", padx=pad_x)

            # 估算按钮宽度，避免频繁 update_idletasks 造成卡顿
            button_width = len(option) * 10 + 40  # 粗略估算

            if current_width + button_width > max_width:
                # 新建一行
                current_row_frame = ttk.Frame(container)
                current_row_frame.pack(side="top", fill="x", padx=5, pady=5)
                current_width = 0

                # 重新打包按钮到新行
                radiobutton.pack_forget()
                radiobutton.pack(side="left", padx=pad_x)

            current_width += button_width + pad_x

        return outer_frame

    # -----------------  辅助方法 -----------------
    def _on_frame_configure(self, event=None):
        """更新 Canvas 的滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _on_canvas_configure(self, event=None):
        """保持内部 Frame 宽度与 Canvas 同步"""
        if event:
            self.canvas.itemconfig(self.canvas_window, width=event.width)

    def _bind_mousewheel(self, widget):
        """给所有子组件绑定鼠标滚轮"""
        widget.bind("<MouseWheel>", self._on_mousewheel)  # Windows
        widget.bind("<Button-4>", self._on_mousewheel)    # Linux 上滚
        widget.bind("<Button-5>", self._on_mousewheel)    # Linux 下滚
        for child in widget.winfo_children():
            self._bind_mousewheel(child)

    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        if (hasattr(event, 'num') and event.num == 4) or (hasattr(event, 'delta') and event.delta > 0):
            self.canvas.yview_scroll(-1, "units")
        elif (hasattr(event, 'num') and event.num == 5) or (hasattr(event, 'delta') and event.delta < 0):
            self.canvas.yview_scroll(1, "units")
        return "break"

    def get_arg_info(self) -> ArgInfo:
        """获取组件参数信息"""
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo(name=field, set_func=self.value_var.set, get_func=self.value_var.get)
        return arg_info


class RadioObviousToolButtonAutoLine(BaseRadioButtonAutoLine):
    """自动换行的明显样式单选工具按钮"""
    def __init__(self,
                 options: str or List[str],
                 default: str = None,
                 bind_func=None,
                 name=None,
                 title="请选择",
                 style="primary",
                 tab_index=0,
                 async_run=False,
                 line_width=500,
                 max_height=280,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super().__init__(options=options,
                         default=default,
                         bind_func=bind_func,
                         name=name,
                         title=title,
                         style=style,
                         button_style="Outline.Toolbutton",
                         tab_index=tab_index,
                         async_run=async_run,
                         line_width=line_width,
                         max_height=max_height,
                         concurrency_mode=concurrency_mode,
                         mode="ToolButton",
                         frame=frame)


class Progressbar(BaseNotebookTool):
    def __init__(self,
                 title: str = "进度条",
                 default: int = 0,
                 max_size: int = 100,
                 name: str = None,
                 style: str = "primary",
                 tab_index: int = 0,
                 async_run: bool = False,
                 concurrency_mode=ConcurrencyModeFlag.SAFE_CONCURRENCY_MODE_FLAG,
                 frame: tkinter.Frame = None):
        super(Progressbar, self).__init__(style=style,
                                          tab_index=tab_index,
                                          name=name,
                                          async_run=async_run,
                                          concurrency_mode=concurrency_mode,
                                          frame=frame)
        self.title = title
        self.default_value = default
        self.max_size = max_size
        
    def set(self, value):
        """设置进度条的值"""
        if hasattr(self, 'progressbar_var'):
            try:
                # 确保值在有效范围内
                value = max(0, min(int(value), self.max_size))
                self.progressbar_var.set(value)
            except Exception as e:
                print(f"设置进度条值失败: {e}")
                
    def get(self):
        """获取进度条的当前值"""
        if hasattr(self, 'progressbar_var'):
            return self.progressbar_var.get()
        return 0

    def progressbar_var_trace(self, *args):
        self.value_var.set(f"进度 {self.progressbar_var.get():.2f}%")
        
    def build(self, *args, **kwargs):
        super().build(*args, **kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master, style="TFrame")
            frame.pack(side="top", fill="x", padx=5, pady=5, expand="yes")

        self.progressbar_var = tkinter.IntVar(frame, value=self.default_value)
        self.value_var = tkinter.StringVar(frame, value=f"进度 {self.default_value:.2f}%")
        self.progressbar_var.trace("w", self.progressbar_var_trace)

        label = ttk.Label(frame,
                          text=self.title,
                          style="TLabel",
                          width=LABEL_WIDTH)
        label.pack(side="left")

        progressbar = ttk.Progressbar(frame,
                                      variable=self.progressbar_var,
                                      style=self.style + "Striped.Horizontal.TProgressbar")
        progressbar.pack(side="left", fill="x", expand="yes", padx=5, pady=2)

        self.value = ttk.Label(frame,
                               textvariable=self.value_var,
                               style="TLabel",
                               width=LABEL_WIDTH)
        self.value.pack(side="left")
        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        arg_info = ArgInfo(name=field, set_func=self.progressbar_var.set, get_func=self.progressbar_var.get)

        return arg_info


class BaseCombine(BaseNotebookTool):
    def __init__(self,
                 tools: BaseNotebookTool or List[BaseNotebookTool],
                 side=HORIZONTAL,
                 title: str = None,
                 text: str = None,
                 style: str = None,
                 tab_index: int = None,
                 frame: tkinter.Frame = None):
        super().__init__(tab_index=tab_index, style=style, frame=frame)
        self.side = "top" if side == HORIZONTAL else "left"
        self.title = title
        self.text = text

        self.tools = tools if isinstance(tools, list) else [tools]

        self.tab_index = tab_index if tab_index else self.tools[0].tab_index

        for tool_id in range(len(self.tools)):
            self.tools[tool_id].tab_index = self.tab_index

    def get_arg_info(self) -> ArgInfo:
        local_info = ArgInfo()
        for tool_id in range(len(self.tools)):
            local_info += self.tools[tool_id].get_arg_info()
        return local_info


class BaseFrameCombine(BaseCombine):
    def build(self, *args, **kwargs):
        super().build(self, *args, **kwargs)

        if self.frame:
            frame = self.frame
        else:
            style_mode = "TLabelframe" if self.title else "TFrame"
            if self.title:
                frame = ttk.LabelFrame(self.master, text=self.title, style=self.style + style_mode)
            else:
                frame = ttk.Frame(self.master, text=self.title, style=self.style + style_mode)
            frame.pack(side="left", anchor="nw", fill="both", expand="yes", padx=DEFAULT_PAD, pady=DEFAULT_PAD)
        if self.text:
            label = ttk.Label(frame,
                              text=self.text,
                              style="TLabel")
            label.pack(side="top", anchor="nw", padx=5)
        for tool in self.tools:
            kwargs["master"] = frame
            tool.build(*args, **kwargs)
        return frame


class HorizontalFrameCombine(BaseFrameCombine):
    def __init__(self,
                 tools: BaseNotebookTool or List[BaseNotebookTool],
                 title=None,
                 style: str = None,
                 text: str = None,
                 tab_index: int = 0,
                 frame: tkinter.Frame = None):
        super().__init__(tools=tools,
                         side=HORIZONTAL,
                         title=title,
                         style=style,
                         text=text,
                         tab_index=tab_index,
                         frame=frame)


class VerticalFrameCombine(BaseFrameCombine):
    def __init__(self,
                 tools: BaseNotebookTool or List[BaseNotebookTool],
                 title=None,
                 style: str = None,
                 text: str = None,
                 tab_index: int = 0,
                 frame: tkinter.Frame = None):
        super().__init__(tools=tools,
                         side=VERTICAL,
                         title=title,
                         style=style,
                         text=text,
                         tab_index=tab_index,
                         frame=frame)


class HorizontalToolsCombine(BaseCombine):
    def __init__(self,
                 tools: BaseNotebookTool or List[BaseNotebookTool],
                 title=None,
                 style: str = None,
                 text: str = None,
                 tab_index: int = None,
                 frame: tkinter.Frame = None):
        super().__init__(tools=tools,
                         side=HORIZONTAL,
                         title=title,
                         style=style,
                         text=text,
                         tab_index=tab_index,
                         frame=frame)

    def build(self, *args, **kwargs):
        super().build(self, *args, **kwargs)

        style_mode = "TLabelframe" if self.title else "TFrame"
        if self.title:
            frame = ttk.LabelFrame(self.master, text=self.title, style=self.style + style_mode)
        else:
            frame = ttk.Frame(self.master, style=self.style + style_mode)
        frame.pack(side="top", fill="x", padx=DEFAULT_PAD, pady=DEFAULT_PAD)
        if self.text:
            label = ttk.Label(frame,
                              text=self.text,
                              style="TLabel")
            label.pack(side="top", anchor="nw", padx=DEFAULT_PAD)
        for tool in self.tools:
            kwargs["master"] = self.frame
            tool.frame = frame
            tool.build(*args, **kwargs)
        return frame


class Label(BaseNotebookTool):
    def __init__(self,
                 name: str = None,
                 text: str = None,
                 title: str = None,
                 alignment: str = LEFT + TOP,
                 style: str = "primary",
                 tab_index: int = 0,
                 frame: tkinter.Frame = None):
        super(Label, self).__init__(name=name,
                                    style=style,
                                    tab_index=tab_index,
                                    frame=frame)
        self.text = text
        self.title = title
        self.alignment = alignment

        self.label_var = tkinter.StringVar(value=self.text)

    def build(self, *args, **kwargs) -> tkinter.Frame:
        super(Label, self).build(*args, **kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master)
            frame.pack(side="top", fill="both", padx=DEFAULT_PAD, pady=DEFAULT_PAD)

        title = ttk.Label(frame,
                          text=self.title,
                          style="TLabel",
                          width=LABEL_WIDTH)
        title.pack(side="left")

        label = ttk.Label(frame,
                          text=self.text,
                          textvariable=self.label_var,
                          style="TLabel")
        # make_anchor(self.alignment)
        label.pack(anchor=make_anchor(self.alignment), padx=DEFAULT_PAD)
        return frame

    def get_arg_info(self) -> ArgInfo:
        field = self.name if self.name else self.__class__.__name__
        local_info = ArgInfo(field, set_func=self.label_var.set, get_func=self.label_var.get)
        return local_info


class ListComponent_5(BaseNotebookTool):
    """ 列表组件 """
    def __init__(self,
                 name: str = None,
                 items: Dict[str, Dict[str, str]] = None,
                 title: str = None,
                 alignment: str = LEFT + TOP,
                 style: str = "primary",
                 tab_index: int = 0,
                 frame: tkinter.Frame = None,
                 height: int = 10):
        super(ListComponent_5, self).__init__(name=name,
                                            style=style,
                                            tab_index=tab_index,
                                            frame=frame)
        self.items = items if items else {}
        self.title = title
        self.alignment = alignment
        self.height = height
        self.select_index = (0,)

    def build(self, *args, **kwargs) -> tkinter.Frame:
        super(ListComponent_5, self).build(*args, **kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master)
            frame.pack(side="top", fill="both", padx=DEFAULT_PAD, pady=DEFAULT_PAD)

        if self.title:
            title_label = ttk.Label(frame,
                                    text=self.title,
                                    style="TLabel",
                                    width=LABEL_WIDTH,
                                    anchor="w")  # 设置文本居左对齐
            title_label.pack(side="top", fill="x")  # 使用fill="x"确保Label填满水平空间

        # 使用Treeview组件
        columns = ("desc", "package", "indoor", "outdoor")
        self.tree = ttk.Treeview(frame, columns=columns, show='headings', height=self.height)
        self.tree.heading("desc", text="描述", anchor="w")
        self.tree.heading("package", text="包名", anchor="w")
        self.tree.heading("indoor", text="室内标准(自动/手动)", anchor="w")
        self.tree.heading("outdoor", text="室外标准(自动/手动)", anchor="w")

        for key, value in self.items.items():
            self.tree.insert("", "end", values=(value.get("desc"), value.get("package"), value.get("indoor"), value.get("outdoor")))

        self.tree.pack(anchor=make_anchor(self.alignment), fill="both", expand=True, padx=DEFAULT_PAD)

        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)

        # 添加输入框和按钮
        self.package_label = ttk.Label(frame, text="包名", style="TLabel")
        self.package_label.pack(side="left")
        self.package_entry = ttk.Entry(frame)
        self.package_entry.pack(side="left", padx=DEFAULT_PAD)

        self.desc_label = ttk.Label(frame, text="描述", style="TLabel")
        self.desc_label.pack(side="left")
        self.desc_entry = ttk.Entry(frame)
        self.desc_entry.pack(side="left", padx=DEFAULT_PAD)

        self.indoor_label = ttk.Label(frame, text="室内标准(自动/手动)", style="TLabel")
        self.indoor_label.pack(side="left")
        self.indoor_entry = ttk.Entry(frame)
        self.indoor_entry.pack(side="left", padx=DEFAULT_PAD)

        self.outdoor_label = ttk.Label(frame, text="室外标准(自动/手动)", style="TLabel")
        self.outdoor_label.pack(side="left")
        self.outdoor_entry = ttk.Entry(frame)
        self.outdoor_entry.pack(side="left", padx=DEFAULT_PAD)

        self.modify_button = ttk.Button(frame, text="修改", command=self.modify_selected_item)
        self.modify_button.pack(side="left", padx=DEFAULT_PAD)

        return frame

    def on_tree_select(self, event):
        """在选择Treeview项时更新输入框的内容"""
        selected_item = self.tree.selection()
        if selected_item:
            item_values = self.tree.item(selected_item, "values")
            desc, package, indoor, outdoor = item_values

            # 更新输入框
            self.package_entry.delete(0, tkinter.END)
            self.package_entry.insert(0, package)

            self.desc_entry.delete(0, tkinter.END)
            self.desc_entry.insert(0, desc)

            self.indoor_entry.delete(0, tkinter.END)
            self.indoor_entry.insert(0, indoor)

            self.outdoor_entry.delete(0, tkinter.END)
            self.outdoor_entry.insert(0, outdoor)

    def modify_selected_item(self):
        """修改选中的Treeview项并更新字典"""
        selected_item = self.tree.selection()
        if selected_item:
            new_package = self.package_entry.get()
            new_desc = self.desc_entry.get()
            new_indoor = self.indoor_entry.get()
            new_outdoor = self.outdoor_entry.get()

            # 更新Treeview显示
            self.tree.item(selected_item, values=(new_desc, new_package, new_indoor, new_outdoor))

            # 找到对应的键并更新字典中的值
            for key, value in self.items.items():
                if value.get("desc") == self.tree.item(selected_item, "values")[0]:
                    self.items[key]['package'] = new_package
                    self.items[key]['desc'] = new_desc
                    self.items[key]['indoor'] = new_indoor
                    self.items[key]['outdoor'] = new_outdoor
                    print(f"Updated {key} successfully.")
                    break
        else:
            print("Error: No item selected.")

    def get_dict_all_data(self):
        """ 获取所有配置信息 """
        return self.items


class ListComponent_4(BaseNotebookTool):
    """ 列表组件 """
    def __init__(self,
                 name: str = None,
                 items: Dict[str, Dict[str, str]] = None,
                 title: str = None,
                 alignment: str = LEFT + TOP,
                 style: str = "primary",
                 tab_index: int = 0,
                 frame: tkinter.Frame = None,
                 height: int = 10):
        super(ListComponent_4, self).__init__(name=name,
                                            style=style,
                                            tab_index=tab_index,
                                            frame=frame)
        self.items = items if items else {}
        self.title = title
        self.alignment = alignment
        self.height = height
        self.select_index = (0,)

    def build(self, *args, **kwargs) -> tkinter.Frame:
        super(ListComponent_4, self).build(*args, **kwargs)
        if self.frame:
            frame = self.frame
        else:
            frame = ttk.Frame(self.master)
            frame.pack(side="top", fill="both", padx=DEFAULT_PAD, pady=DEFAULT_PAD)

        if self.title:
            title_label = ttk.Label(frame,
                                    text=self.title,
                                    style="TLabel",
                                    width=LABEL_WIDTH,
                                    anchor="w")  # 设置文本居左对齐
            title_label.pack(side="top", fill="x")  # 使用fill="x"确保Label填满水平空间

        # 使用Treeview组件
        columns = ("desc", "package", "auto", "manual")
        self.tree = ttk.Treeview(frame, columns=columns, show='headings', height=self.height)
        self.tree.heading("desc", text="描述", anchor="w")
        self.tree.heading("package", text="包名", anchor="w")
        self.tree.heading("auto", text="自动标准", anchor="w")
        self.tree.heading("manual", text="手动标准", anchor="w")

        for key, value in self.items.items():
            self.tree.insert("", "end", values=(value.get("desc"), value.get("package"), value.get("auto"), value.get("manual")))

        self.tree.pack(anchor=make_anchor(self.alignment), fill="both", expand=True, padx=DEFAULT_PAD)

        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)

        # 添加输入框和按钮
        self.package_label = ttk.Label(frame, text="包名", style="TLabel")
        self.package_label.pack(side="left")
        self.package_entry = ttk.Entry(frame)
        self.package_entry.pack(side="left", padx=DEFAULT_PAD)

        self.desc_label = ttk.Label(frame, text="描述", style="TLabel")
        self.desc_label.pack(side="left")
        self.desc_entry = ttk.Entry(frame)
        self.desc_entry.pack(side="left", padx=DEFAULT_PAD)

        self.auto_label = ttk.Label(frame, text="自动标准", style="TLabel")
        self.auto_label.pack(side="left")
        self.auto_entry = ttk.Entry(frame)
        self.auto_entry.pack(side="left", padx=DEFAULT_PAD)

        self.manual_label = ttk.Label(frame, text="手动标准", style="TLabel")
        self.manual_label.pack(side="left")
        self.manual_entry = ttk.Entry(frame)
        self.manual_entry.pack(side="left", padx=DEFAULT_PAD)

        self.modify_button = ttk.Button(frame, text="修改", command=self.modify_selected_item)
        self.modify_button.pack(side="left", padx=DEFAULT_PAD)

        return frame

    def on_tree_select(self, event):
        """在选择Treeview项时更新输入框的内容"""
        selected_item = self.tree.selection()
        if selected_item:
            item_values = self.tree.item(selected_item, "values")
            desc, package, auto, manual = item_values

            # 更新输入框
            self.package_entry.delete(0, tkinter.END)
            self.package_entry.insert(0, package)

            self.desc_entry.delete(0, tkinter.END)
            self.desc_entry.insert(0, desc)

            self.auto_entry.delete(0, tkinter.END)
            self.auto_entry.insert(0, auto)

            self.manual_entry.delete(0, tkinter.END)
            self.manual_entry.insert(0, manual)

    def modify_selected_item(self):
        """修改选中的Treeview项并更新字典"""
        selected_item = self.tree.selection()
        if selected_item:
            new_package = self.package_entry.get()
            new_desc = self.desc_entry.get()
            new_auto = self.auto_entry.get()
            new_manual = self.manual_entry.get()

            # 更新Treeview显示
            self.tree.item(selected_item, values=(new_desc, new_package, new_auto, new_manual))

            # 找到对应的键并更新字典中的值
            for key, value in self.items.items():
                if value.get("desc") == self.tree.item(selected_item, "values")[0]:
                    self.items[key]['package'] = new_package
                    self.items[key]['desc'] = new_desc
                    self.items[key]['auto'] = int(new_auto)
                    self.items[key]['manual'] = int(new_manual)
                    print(f"Updated {key} successfully.")
                    break
        else:
            print("Error: No item selected.")

    def get_dict_all_data(self):
        """ 获取所有配置信息 """
        return self.items