# 弹窗监控功能使用说明

## 功能概述
弹窗监控功能用于在测试过程中自动检测和处理应用弹窗，例如权限请求、更新提示、广告等。该功能会在测试开始前启动监控，并在测试结束后停止监控，确保测试过程不会被弹窗干扰。

## 界面控制
在主界面头部栏中，有一个"弹窗监控"开关，用于控制是否启用弹窗监控功能。
- 开启状态：测试过程中会自动检测和处理弹窗
- 关闭状态：测试过程中不会监控弹窗

## 配置文件
弹窗监控功能使用`popup_config.json`配置文件来定义弹窗规则。该文件位于程序根目录下，如果不存在，系统会使用默认规则。

### 配置文件格式
```json
{
    "rules": [
        {
            "name": "权限请求弹窗",
            "type": "text",
            "keywords": ["允许", "权限", "访问"],
            "action": "click",
            "target": "允许"
        },
        {
            "name": "更新提示弹窗",
            "type": "text",
            "keywords": ["更新", "新版本"],
            "action": "click",
            "target": "取消"
        }
    ],
    "enabled": true,
    "check_interval": 2,
    "max_retries": 3
}
```

### 配置参数说明

#### 全局参数
- `enabled`: 是否启用弹窗监控功能，`true`或`false`
- `check_interval`: 检查间隔时间（秒）
- `max_retries`: 最大重试次数

#### 规则参数
每个规则包含以下参数：

- `name`: 规则名称，用于日志记录
- `type`: 规则类型，可选值：
  - `text`: 基于文本内容识别弹窗
  - `id`: 基于资源ID识别弹窗
  - `xpath`: 基于XPath识别弹窗
- `keywords`: 关键词列表，仅在`type`为`text`时使用
- `resource_id`: 资源ID，仅在`type`为`id`时使用
- `xpath`: XPath表达式，仅在`type`为`xpath`时使用
- `action`: 处理动作，可选值：
  - `click`: 点击指定元素
  - `click_center`: 点击元素中心位置
  - `back`: 按返回键
- `target`: 点击目标文本，仅在`action`为`click`且`type`为`text`时使用

## 添加新规则
要添加新的弹窗规则，只需编辑`popup_config.json`文件，在`rules`数组中添加新的规则对象。例如：

```json
{
    "name": "位置权限弹窗",
    "type": "text",
    "keywords": ["位置", "定位"],
    "action": "click",
    "target": "仅在使用该应用时允许"
}
```

## 工作原理
1. 测试开始前，系统会启动弹窗监控线程
2. 监控线程会按照配置的间隔时间定期检查屏幕上是否有符合规则的弹窗
3. 如果检测到弹窗，系统会根据规则执行相应的操作（点击按钮、返回等）
4. 测试结束后，系统会自动停止弹窗监控线程

## 日志记录
弹窗监控功能会记录所有检测到的弹窗和处理动作，可以在日志中查看相关信息。

## 注意事项
1. 确保`popup_config.json`文件格式正确，否则系统将使用默认规则
2. 弹窗规则的优先级按照在配置文件中的顺序，靠前的规则优先匹配
3. 如果修改配置文件后没有生效，请重启应用 