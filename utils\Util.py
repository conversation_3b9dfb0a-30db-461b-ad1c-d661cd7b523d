import os
import time

import win32api

from utils.adb.ExAdb import ExAdb
from utils.GetBrightnessReduction import GetBrightnessReduction


class Util:

    def __init__(self):
        self.adb = ExAdb()
        self.brightnessModel = GetBrightnessReduction()

    def connect_info_str(self, device_list):
        """ 处理设备连接信息 """

        res = ""
        list_len = len(device_list)
        i = 0
        for item in device_list:
            device_name = self.adb.get_device_name(item)
            res += f"DevID: {item}\n设备名称: {device_name}连接状态: 已连接"
            i += 1
            if i < list_len:
                res += "\n\n"
        return res

    def get_nit_value(self, device_id):
        """ 获取当前的 nit 值 """
        return self.brightnessModel.get_current_nit_value(device_id)

    def find_optimal_dbv(self, target_nit_value=350, tolerance=0.01, device_id=None):
        """ 寻找最优的 dbv 值 返回的是0-255的值"""
        # 开启亮度捕获线程
        if device_id is None:
            return
        self.brightnessModel.start_threads()
        time.sleep(5)
        dbv_max = 255
        min_dbv = 1
        max_dbv = dbv_max

        # 二分查找法
        while min_dbv <= max_dbv:
            mid_dbv = (min_dbv + max_dbv) // 2

            # 切换到中间的 dbv 值
            self.adb.switch_brightness_255(brightness=mid_dbv, device_id=device_id)
            time.sleep(2)
            # 获取当前的 nit_value
            nit_value = int(self.get_nit_value(device_id))

            # 检查 nit_value 是否在目标值附近
            if abs(nit_value - target_nit_value) <= tolerance:
                return mid_dbv
            elif nit_value < target_nit_value:
                min_dbv = mid_dbv + 1
            else:
                max_dbv = mid_dbv - 1

        # 如果找不到精确的值，返回最接近的
        return mid_dbv

    def get_user_name(self):
        """ 获取用户名 """
        return win32api.GetUserNameEx(3)

    def get_user_job_id(self):
        """ 获取用户的工号 """
        return os.getlogin()

    def get_current_date(self):
        """ 获取当前日期 """
        return time.strftime("%Y-%m-%d", time.localtime())
