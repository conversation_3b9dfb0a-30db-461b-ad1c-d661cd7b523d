from typing import Optional, List, Callable, Dict, Any, Union
from component.GlobalInstanceBase import GlobalInstanceBase
from queues.TaskQueueManager import TaskQueueManager
from queues.TestQueue import TestQueue
from queues.TestFourQueue import TestFourQueue
from utils.adb.ExAdb import ExAdb


class QueueController:
    """队列控制器，负责UI与TaskQueueManager之间的交互"""
    
    def __init__(self, guiModel, logs):
        """
        初始化队列控制器
        
        Args:
            guiModel: GUI模型实例
            logs: 日志记录器实例
        """
        self.guiModel = guiModel
        self.logs = logs
        self.global_instance = None
        self.queue_manager = TaskQueueManager(logs=logs)
        self.adb = ExAdb()
        
        # 设备ID缓存，避免重复获取
        self._device_id_cache = None
        
        # 进度条和列表框引用
        self.progress_bar = None
        self.queue_list_box = None
        self.queue_total_progress = None
    
    def set_global_instance(self, global_instance: GlobalInstanceBase):
        """设置全局实例"""
        self.global_instance = global_instance
    
    def set_ui_components(self, progress_bar=None, queue_list_box=None, queue_total_progress=None):
        """设置UI组件引用"""
        self.progress_bar = progress_bar
        self.queue_list_box = queue_list_box
        self.queue_total_progress = queue_total_progress
        
        # 设置回调函数
        self.queue_manager.set_callbacks(
            on_progress=self._on_queue_progress,
            on_queue_finished=self._on_queue_finished,
            on_all_finished=self._on_all_queues_finished
        )
    
    def add_to_execution_queue(self, test_queue: Union[TestQueue, TestFourQueue]) -> bool:
        """
        将测试队列添加到执行队列
        
        Args:
            test_queue: 测试队列实例
            
        Returns:
            bool: 是否成功添加
        """
        if test_queue is None:
            self.logs.error("无法添加空队列")
            return False
            
        result = self.queue_manager.add_queue(test_queue)
        
        # 更新UI
        self._refresh_queue_list()
        
        return result
    
    def clear_execution_queue(self) -> None:
        """清空执行队列"""
        self.queue_manager.clear()
        
        # 更新UI
        self._refresh_queue_list()
        
        # 重置进度条
        if self.progress_bar:
            self.progress_bar.set(0)
        if self.queue_total_progress:
            self.queue_total_progress.set(0)
    
    def execute_queues(self, args: Dict[str, Any] = None) -> bool:
        """
        开始执行所有队列

        Args:
            args: UI传入的参数字典

        Returns:
            bool: 是否成功启动执行
        """
        # 检查是否有队列
        if self.queue_manager.is_empty():
            self.logs.warning("执行队列为空")
            return False
        
        # 获取设备ID
        device_id = self._get_device_id()
        if not device_id:
            self.logs.error("未连接设备")
            return False
        
        # 重置进度条
        if self.progress_bar:
            self.progress_bar.set(0)
        if self.queue_total_progress:
            self.queue_total_progress.set(0)
        
        # 开始执行队列
        result = self.queue_manager.execute_all(device_id)

        return result
    
    def stop_execution(self) -> None:
        """停止队列执行"""
        self.queue_manager.stop()
    
    def _get_device_id(self) -> Optional[str]:
        """获取当前设备ID"""
        # 如果缓存为空，重新获取设备列表
        if self._device_id_cache is None:
            device_list = self.adb.get_device_list()
            if device_list and len(device_list) > 0:
                self._device_id_cache = device_list[0]
        
        return self._device_id_cache
    
    def _refresh_queue_list(self):
        """刷新队列列表UI"""
        if self.queue_list_box:
            # 更新列表框内容
            queue_titles = self.queue_manager.get_queue_titles()
            
            # 打印调试信息
            print(f"刷新队列列表，共 {len(queue_titles)} 个队列")
            for i, title in enumerate(queue_titles):
                print(f"  {i+1}. {title}")
            
            # 清空并重新添加
            self.queue_list_box.clear_all_tasks()
            for title in queue_titles:
                try:
                    self.queue_list_box.add_item(title)
                    print(f"成功添加队列：{title}")
                except Exception as e:
                    print(f"添加队列 '{title}' 失败: {e}")
    
    def _on_queue_progress(self, queue_index: int, total_queues: int, current_progress: int):
        """
        队列进度回调
        
        Args:
            queue_index: 当前队列索引
            total_queues: 队列总数
            current_progress: 当前队列进度(0-100)
        """
        # 更新当前队列进度条
        if self.progress_bar:
            self.progress_bar.set(current_progress)
        
        # 计算总体进度
        if self.queue_total_progress and total_queues > 0:
            # 每个队列权重相同，总进度 = (已完成队列 + 当前队列进度比例) / 总队列数 * 100
            total_progress = ((queue_index + current_progress / 100) / total_queues) * 100
            self.queue_total_progress.set(int(total_progress))
        
        # 高亮显示当前执行的队列
        if self.queue_list_box:
            self.queue_list_box.select_item(queue_index)
    
    def _on_queue_finished(self, queue_index: int, queue_title: str):
        """
        单个队列完成回调
        
        Args:
            queue_index: 队列索引
            queue_title: 队列标题
        """
        self.logs.info(f"队列 '{queue_title}' 执行完成")
    
    def _on_all_queues_finished(self):
        """所有队列完成回调"""
        self.logs.info("所有队列执行完成")
        
        # 取消列表选中状态
        if self.queue_list_box:
            self.queue_list_box.unselect_all()
    
    def get_queue_count(self) -> int:
        """获取队列数量"""
        return self.queue_manager.get_queue_count() 