import os
import time
from utils.Util import Util
from component.GlobalInstanceBase import GlobalInstanceBase
from qgui import MessageBox
from qgui.notebook_tools import RadioButton
from enums.PackageName import PackageName
from queues.TestQueue import TestQueue
from utils.adb.ExAdb import ExAdb
from utils.PresetsConfig import PresetsConfig
from utils.ExcelSave import ExcelSave
from utils.PopupConfig import PopupConfig
from utils.PopupMonitor import PopupMonitor


class MainController:
    """ 亮度降幅5.0控制器 """
    def __init__(self, guiModel, logs):
        self.guiModel = guiModel
        self.global_instance = None
        self.queue_list = {}
        self.test_queue = TestQueue()
        self.is_auto_brightness = False
        self.adb = ExAdb()
        self.logs = logs
        self.util = Util()
        # 初始化设备连接状态
        self.is_device_connected = False
        # 初始化预设配置
        self.presets_config = PresetsConfig(version="5.0")
        # 初始化弹窗配置和监控
        self.popup_config = PopupConfig()
        self.popup_monitor = PopupMonitor()
        # 弹窗监控开关状态
        self.is_popup_monitor_enabled = self.popup_config.is_enabled()
        # 设置日志实例
        self.test_queue.set_logs(self.logs)
        self.popup_monitor.set_logs(self.logs)
        
        # 设置TestQueue的弹窗监控实例
        self.test_queue.popup_monitor = self.popup_monitor

    def set_global_instance(self, global_instance: GlobalInstanceBase):
        self.global_instance = global_instance
        
    def init_ui_status(self):
        """初始化UI状态"""
        # 初始化导航栏弹窗监控状态
        status = "已启用" if self.is_popup_monitor_enabled else "已禁用"
        try:
            self.guiModel.update_navigation_info(name="PopupMonitorStatus", info=status)
        except Exception as e:
            # 忽略可能的初始化错误
            self.logs.warning(f"初始化导航栏状态失败: {e}")
        
    def popup_monitor_switch(self, args: dict):
        """弹窗监控开关"""
        try:
            # 切换状态
            self.is_popup_monitor_enabled = not self.is_popup_monitor_enabled
            self.popup_config.set_enabled(self.is_popup_monitor_enabled)
            
            if self.is_popup_monitor_enabled:
                self.logs.info("弹窗监控已启用")
                MessageBox.info("弹窗监控已启用")
                # 更新导航栏信息
                self.guiModel.update_navigation_info(name="PopupMonitorStatus", info="已启用")
            else:
                # 如果监控正在运行，则停止
                self.popup_monitor.stop_monitor()
                self.logs.info("弹窗监控已禁用")
                MessageBox.info("弹窗监控已禁用")
                # 更新导航栏信息
                self.guiModel.update_navigation_info(name="PopupMonitorStatus", info="已禁用")
        except Exception as e:
            self.logs.error(f"切换弹窗监控状态失败: {e}")

    def click(self, args: dict):
        if not self.test_queue.get_is_start_brightness_thread():
            MessageBox.info("未连接设备，请点击左上角连接设备")
            return
        args["进度条"].set(0)
        self.test_queue.set_progress_bar(args["进度条"])

        # 启动亮度线程
        self.test_queue.start_brightness_thread()
        
        # 重置文件名，确保生成新文件
        self.test_queue.set_file_name(None)
        device_list = self.adb.get_device_list()
        if len(device_list) < 1:
            self.test_queue.stop_brightness_thread()  # Stop thread if no device
            return
            
        # 生成一个基础文件名
        base_file_name = self.test_queue.get_new_file_name(device_list[0], self.test_queue.get_current_time())
        self.test_queue.set_file_name(base_file_name)
        
        # 获取手动选择的队列
        queue_list = self.global_instance.get_instance("QueueDisplay").get_all_task_names()
        self.test_queue.set_queue_data(queue_list)
        self.test_queue.set_is_auto_brightness(self.is_auto_brightness)
        
        # 设置文件信息和测试名称
        sheet_name = "自动" if self.is_auto_brightness else "手动"
        
        radio_selection = args['RadioObviousTool'].get()
        if "外销" in radio_selection:
            sheet_name = "外销" + sheet_name

        self.test_queue.set_file_info(sheet_name)
        
        # 执行测试
        self.test_queue.start_test_threads(sheet_name=sheet_name, add_separator=True, is_headers=False)
        
        # 添加元数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.test_queue.get_file_name(), sheet_name=sheet_name, append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name=sheet_name)
            # 保存文件后立即关闭，避免持续占用
            excel_model.save(self.test_queue.get_file_name())
            break
            
        # 停止亮度线程
        self.test_queue.stop_brightness_thread()
        
        # 显示测试完成提示
        MessageBox.info("测试完成")
        
        # 在控制台输出详细信息
        self.logs.info(f"测试完成，文件保存为：{self.test_queue.get_file_name()}")

    def add_queue_package(self, args: dict):
        new_name = args['InputPackageName'].get()
        # 新增队列
        self.global_instance.get_instance("QueueDisplay").add_task(new_name)
        self.queue_list[new_name] = new_name

    def add_queue_sub(self, item: tuple):
        name, flag = item
        if flag:
            # 新增队列
            self.global_instance.get_instance("QueueDisplay").add_task(name)
        else:
            # 删除队列
            self.global_instance.get_instance("QueueDisplay").remove_task(name)

    def queue_click(self, name: str):
        # 更改多选按钮状态
        self.global_instance.get_instance("CheckObviousToolLine").update_box(name, False)

    def set_is_auto_brightness(self, is_auto_brightness):
        self.is_auto_brightness = is_auto_brightness
        self.global_instance.get_instance("auto_brightness_switch").set_status(self.is_auto_brightness, "自动亮度")
        if self.is_auto_brightness:
            self.adb.start_auto_brightness()
        else:
            self.adb.close_auto_brightness()

    def auto_brightness_switch(self, args: dict):
        if args['auto_brightness_switch-自动亮度'].get() == "1":
            self.set_is_auto_brightness(True)
        else:
            self.set_is_auto_brightness(False)

    def preinstall_click(self, args: dict):
        """ 预设单选按钮 """
        radio_selection = args['RadioObviousTool'].get()

        # 清空队列
        self.global_instance.get_instance("QueueDisplay").clear_all_tasks()
        self.global_instance.get_instance("CheckObviousToolLine").update_box_all(False)

        preset_key = None
        fallback_packages = []
        
        if radio_selection == "内_OLED_自动":
            preset_key = "内_OLED_自动"
            fallback_packages = PackageName.get_package_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "内_OLED_手动":
            preset_key = "内_OLED_手动"
            fallback_packages = PackageName.get_package_manual_list()
            self.set_is_auto_brightness(False)
        elif radio_selection == "外_OLED_自动":
            preset_key = "外_OLED_自动"
            fallback_packages = PackageName.get_package_ex_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "外_OLED_手动":
            preset_key = "外_OLED_手动"
            fallback_packages = PackageName.get_package_ex_manual_list()
            self.set_is_auto_brightness(False)
        elif radio_selection == "内_LCD_自动":
            preset_key = "内_LCD_自动"
            fallback_packages = PackageName.get_package_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "内_LCD_手动":
            preset_key = "内_LCD_手动"
            fallback_packages = PackageName.get_package_manual_list()
            self.set_is_auto_brightness(False)
        elif radio_selection == "外_LCD_自动":
            preset_key = "外_LCD_自动"
            fallback_packages = PackageName.get_package_ex_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "外_LCD_手动":
            preset_key = "外_LCD_手动"
            fallback_packages = PackageName.get_package_ex_manual_list()
            self.set_is_auto_brightness(False)
        elif radio_selection == "清空队列":
            return  # Do nothing else
        # 向下兼容旧的预设名称
        elif radio_selection == "自动":
            preset_key = "auto"
            fallback_packages = PackageName.get_package_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "手动":
            preset_key = "manual"
            fallback_packages = PackageName.get_package_manual_list()
            self.set_is_auto_brightness(False)
        elif radio_selection == "外销自动":
            preset_key = "ex_auto"
            fallback_packages = PackageName.get_package_ex_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "外销手动":
            preset_key = "ex_manual"
            fallback_packages = PackageName.get_package_ex_manual_list()
            self.set_is_auto_brightness(False)

        if preset_key:
            app_list = self.presets_config.get_preset_apps(preset_key)
            if app_list:
                for app_name in app_list:
                    self.global_instance.get_instance("QueueDisplay").add_task(app_name)
            else:
                # Fallback to hardcoded lists
                for item in fallback_packages:
                    self.global_instance.get_instance("QueueDisplay").add_task(item.value.get("desc"))

    def testing_standard_click(self, args: dict):
        """ 测试标准切换 """
        if args['TestingStandardTool'].get() == "室内标准":
            self.test_queue.set_test_standard("indoor")
        elif args['TestingStandardTool'].get() == "室外标准":
            self.test_queue.set_test_standard("outdoor")

    def connect_device(self, args: dict):
        """ 连接设备 """
        # 获取设备列表
        device_list = self.adb.get_device_list()
        if not device_list or len(device_list) == 0:
            info_str = "未检测到设备，请检查设备连接"
            self.logs.warning(info_str)
            self.guiModel.update_navigation_info(name="ConnectDevice", info=info_str)
            MessageBox.info(info_str)
            return False
        
        # 执行root操作
        try:
            self.logs.info("正在尝试获取root权限...")
            self.adb.get_root()
            self.logs.info("root操作完成")
        except Exception as e:
            self.logs.error(f"获取root权限失败: {e}")
        
        # 安装ATX.apk
        try:
            self.logs.info("正在安装ATX.apk...")
            if self.adb.install_atx_app():
                self.logs.info("ATX.apk安装成功")
            else:
                self.logs.warning("ATX.apk安装失败，可能影响部分功能")
        except Exception as e:
            self.logs.error(f"安装ATX.apk失败: {e}")
        
        # 更新设备连接信息
        info_str = self.util.connect_info_str(device_list)
        self.logs.info("\n连接设备："+info_str)
        self.guiModel.update_navigation_info(name="ConnectDevice", info=info_str)
        self.test_queue.start_brightness_thread()
        
        # 设置设备已连接标志
        self.is_device_connected = True
        
        # 设置弹窗监控的设备ID
        if device_list and len(device_list) > 0:
            self.popup_monitor.connect_device(device_list[0])
        
        return True

    def is_device_connected(self):
        """
        检查设备是否已连接
        :return: 设备连接状态
        """
        return getattr(self, 'is_device_connected', False)

    def save_preset(self, preset_key):
        """保存当前队列为预设"""
        queue_list = self.global_instance.get_instance("QueueDisplay").get_all_task_names()
        success = self.presets_config.save_preset_apps(preset_key, queue_list)
        if success:
            MessageBox.info(f"保存预设 {self.presets_config.get_preset_name(preset_key)} 成功")
        else:
            MessageBox.info(f"保存预设 {preset_key} 失败")

    def reload_presets(self):
        """重新加载预设配置"""
        result = self.presets_config.reload()
        
        # 获取当前选中的预设
        try:
            current_preset = self.guiModel.get_data_by_name("RadioObviousTool")
            
            # 如果当前有选中预设，则重新加载
            if current_preset and current_preset != "清空队列":
                # 清空队列
                self.global_instance.get_instance("QueueDisplay").clear_all_tasks()
                self.global_instance.get_instance("CheckObviousToolLine").update_box_all(False)
                
                preset_key = None
                # 新预设格式
                if current_preset in ["内_OLED_自动", "内_OLED_手动", "外_OLED_自动", "外_OLED_手动", 
                                    "内_LCD_自动", "内_LCD_手动", "外_LCD_自动", "外_LCD_手动"]:
                    preset_key = current_preset
                # 旧预设格式兼容
                elif current_preset == "自/手动亮度":
                    preset_key = "auto_manual"
                elif current_preset == "外销自/手动亮度":
                    preset_key = "ex_auto_manual"
                elif current_preset == "室外自/手动亮度":
                    preset_key = "outdoor"
                elif current_preset == "自动":
                    preset_key = "auto"
                elif current_preset == "手动":
                    preset_key = "manual"
                elif current_preset == "外销自动":
                    preset_key = "ex_auto"
                elif current_preset == "外销手动":
                    preset_key = "ex_manual"
                
                if preset_key:
                    # 获取最新的预设应用列表
                    app_list = self.presets_config.get_preset_apps(preset_key)
                    if app_list:
                        for app_name in app_list:
                            # 新增队列
                            self.global_instance.get_instance("QueueDisplay").add_task(app_name)
        except Exception as e:
            print(f"刷新预设失败: {e}")
            
        return result

    def add_to_execution_queue(self, args: dict = None):
        """
        将当前队列添加到执行队列
        
        Args:
            args: UI参数字典，可选
        """
        # 确认有队列控制器可用
        queue_controller = self.global_instance.get_instance("QueueController")
        if queue_controller is None:
            self.logs.error("队列控制器未初始化")
            MessageBox.info("队列控制器未初始化")
            return False
        
        # 获取当前队列设置
        queue_list = self.global_instance.get_instance("QueueDisplay").get_all_task_names()
        
        # 如果队列为空，提示用户
        if not queue_list or len(queue_list) == 0:
            self.logs.warning("当前队列为空，无法添加到执行队列")
            MessageBox.info("当前队列为空，请选择应用后再添加")
            return False
        
        # 创建一个TestQueue实例
        test_queue = TestQueue()
        test_queue.set_logs(self.logs)
        test_queue.set_queue_data(queue_list)
        test_queue.set_is_auto_brightness(self.is_auto_brightness)
        
        # 设置队列标题
        radio_selection = None
        if args and 'RadioObviousTool' in args:
            radio_selection = args['RadioObviousTool'].get()
        
        # 根据当前选项生成队列标题
        if radio_selection:
            title = f"5.0 - {radio_selection}"
        else:
            title = f"5.0 - {'自动亮度' if self.is_auto_brightness else '手动亮度'}"
        
        test_queue.set_queue_title(title)
        
        # 添加到执行队列
        success = queue_controller.add_to_execution_queue(test_queue)
        
        if success:
            self.logs.info(f"已将队列 '{title}' 添加到执行队列")
            MessageBox.info(f"已将队列 '{title}' 添加到执行队列")
            return True
        else:
            MessageBox.info("添加队列失败")
            return False


