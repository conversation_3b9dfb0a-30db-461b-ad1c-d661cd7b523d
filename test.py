from utils.Util import Util
util = Util()
print(util.get_user_name())
exit()
current_nit_value = 96
drop_value = 100

if drop_value - 5 <= current_nit_value <= drop_value + 5:
    # 如果在范围内，执行相应的操作
    print("current_nit_value 在 drop_value 的 ±5 范围内")
else:
    # 如果不在范围内，执行其他操作
    print("current_nit_value 不在 drop_value 的 ±5 范围内")
exit()
from utils.adb.ExAdb import ExAdb
from utils.Util import Util

adb = ExAdb()
util = Util()
device_id = '10AE3Z06HH000FQ'
# num_value = adb.get_auto_brightness(device_id="10AE3Z06HH000FQ")
nit_value = util.find_optimal_dbv(target_nit_value=100, device_id=device_id)
print(nit_value)


