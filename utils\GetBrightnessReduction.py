import re
import subprocess
import threading
import time
import os
from datetime import datetime

from utils.adb.BaseAdb import BaseAdb
from queue import Queue


class GetBrightnessReduction(BaseAdb):
    def __init__(self):
        super().__init__()
        """ 初始化 """
        self.log_queue = Queue()
        self.device_nit_values = {}
        self.device_drop_values = {}
        self.device_current_nit_values = {}
        self.running = False  # 控制线程运行的标志
        self.threads = []  # 存储所有创建的线程
        self.processes = {}  # 存储所有创建的进程

        # 数据持久化相关属性
        self.data_logging_enabled = False
        self.current_scene = "未知场景"
        self.data_files = {}  # 存储每个设备的数据文件句柄
        self.raw_files = {}   # 存储每个设备的原始数据文件句柄
        self.file_lock = threading.Lock()  # 文件写入锁
        pass

    def adb_logcat_nit(self, device_id):
        """ 从日志文件中持续读取nit变化 """
        # MTK 亮度识别
        cmd = ["adb", "-s", device_id, "shell", "logcat", "|", "grep", "VivoBrightnessPolicy"]
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        self.processes[device_id] = process

        while self.running:
            line = process.stdout.readline()
            if not line and process.poll() is not None:
                break
            if line and "Get target percent" in line:
                # 记录原始数据到文件
                if self.data_logging_enabled and device_id in self.raw_files:
                    self._write_raw_data(device_id, line)

                self.log_queue.put((device_id, line))  # 将捕获的日志行放入队列中

        # 确保进程被终止
        if process.poll() is None:
            process.terminate()

    def extract_nit_value(self, log_line):
        """ 处理字符串获取其中的nit值 """
        match = re.search(r'target nit (\d+)', log_line)
        if match:
            return match.group(1)  # 返回数字字符串
        return None

    def extract_current_nit_value(self, log_line):
        """ 处理字符串获取其中的nit值 - 当前nit值（不受降幅影响） """
        match = re.search(r'current nit (\d+)', log_line)
        if match:
            return match.group(1)  # 返回数字字符串
        return None

    def extract_drop_value(self, log_line):
        """ 处理字符串获取其中的降幅值 """
        match = re.search(r'target percent ([0-9]+(?:\.[0-9]+)?)%', log_line)
        if match:
            return match.group(1)  # 返回数字字符串
        return None

    def start_threads(self):
        """ 创建并启动线程，使用self.adb_logcat_nit作为目标函数 """
        if self.running:
            return  # 如果已经在运行，则不重复启动

        self.running = True
        device_list = self.get_device_list()
        self.threads = []

        for device_id in device_list:
            thread = threading.Thread(target=self.adb_logcat_nit, args=(device_id,))
            thread.daemon = True
            self.threads.append(thread)
            thread.start()

        return True

    def stop_threads(self):
        """ 停止所有线程 """
        if not self.running:
            return False  # 如果没有在运行，则无需停止

        self.running = False

        # 终止所有子进程
        for device_id, process in self.processes.items():
            if process.poll() is None:  # 如果进程仍在运行
                process.terminate()
                try:
                    process.wait(timeout=5)  # 等待进程终止，最多5秒
                except subprocess.TimeoutExpired:
                    process.kill()  # 如果超时，强制终止

        # 清空进程字典
        self.processes.clear()

        # 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=2)  # 给每个线程2秒时间结束

        # 清空线程列表
        self.threads.clear()

        # 停止数据记录
        self.disable_data_logging()

        return True

    def get_value(self):
        while not self.log_queue.empty():
            device_id, log_line = self.log_queue.get()
            nit_value = self.extract_nit_value(log_line.strip())
            current_nit_value = self.extract_current_nit_value(log_line.strip())
            drop_value = self.extract_drop_value(log_line.strip())

            if nit_value:
                self.device_nit_values[device_id] = nit_value
            if drop_value:
                self.device_drop_values[device_id] = drop_value
            if current_nit_value:
                self.device_current_nit_values[device_id] = current_nit_value

            # 记录处理后的数据到文件
            if self.data_logging_enabled and device_id in self.data_files:
                if nit_value and current_nit_value and drop_value:
                    self._write_processed_data(device_id, nit_value, current_nit_value, drop_value)

    def get_current_nit_value(self, device_id):
        """ 获取指定设备的当前nit值 """
        self.get_value()
        return self.device_nit_values.get(device_id, "未获取到nit值")

    def get_current_brightness_drop(self, device_id):
        """ 获取指定设备的当前亮度降幅 """
        self.get_value()
        return self.device_drop_values.get(device_id, "未获取到降幅值")

    def get_current_absolute_nit_value(self, device_id):
        """ 获取指定设备的当前nit值 - 不受降幅影响的nit值 """
        self.get_value()
        return self.device_current_nit_values.get(device_id, "未获取到nit值")

    def is_running(self):
        """ 检查线程是否正在运行 """
        return self.running

    # ==================== 数据持久化相关方法 ====================

    def enable_data_logging(self, base_filename=None):
        """
        启用数据记录功能，使用与ExcelSave.py相同的文件夹管理逻辑

        Args:
            base_filename (str, optional): 基础文件名，如果为None则自动生成
        """
        if self.data_logging_enabled:
            return  # 已经启用，无需重复操作

        self.data_logging_enabled = True

        # 为每个设备创建数据文件
        device_list = self.get_device_list()
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        for device_id in device_list:
            if base_filename:
                # 使用提供的基础文件名，复用ExcelSave的文件夹逻辑
                folder_name, data_filename, raw_filename = self._prepare_file_paths(base_filename, device_id)
            else:
                # 自动生成文件名（无文件夹）
                data_filename = f"{device_id}_{timestamp}_brightness_data.txt"
                raw_filename = f"{device_id}_{timestamp}_raw_logcat.txt"
                folder_name = None

            try:
                # 创建数据文件
                data_file = open(data_filename, 'w', encoding='utf-8')
                raw_file = open(raw_filename, 'w', encoding='utf-8')

                # 写入文件头
                data_file.write("时间戳,场景,目标亮度(nit),当前亮度(nit),降幅(%)\n")
                raw_file.write(f"# 原始logcat数据 - 设备: {device_id}\n")
                raw_file.write(f"# 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

                self.data_files[device_id] = data_file
                self.raw_files[device_id] = raw_file

                print(f"为设备 {device_id} 创建数据记录文件:")
                if folder_name:
                    print(f"  文件夹: {folder_name}")
                print(f"  处理后数据: {data_filename}")
                print(f"  原始数据: {raw_filename}")

            except Exception as e:
                print(f"创建设备 {device_id} 的数据文件失败: {e}")

    def _prepare_file_paths(self, base_filename, device_id):
        """
        准备文件路径，复用ExcelSave.py的文件夹管理逻辑

        Args:
            base_filename (str): 基础文件名（包含.xlsx扩展名）
            device_id (str): 设备ID

        Returns:
            tuple: (文件夹名, 数据文件路径, 原始文件路径)
        """
        # 移除.xlsx扩展名，获取基础名称
        if base_filename.endswith('.xlsx'):
            base_name = base_filename[:-5]  # 移除.xlsx
        else:
            base_name = base_filename

        # 准备文件路径（与ExcelSave.py逻辑一致）
        basename = os.path.basename(base_name)
        dirname = os.path.dirname(base_name) if os.path.dirname(base_name) else "."
        folder_name = basename  # 文件夹名就是文件名（去掉扩展名）

        # 检查文件是否已经在同名文件夹中
        parent_dir = os.path.basename(dirname)

        if parent_dir != folder_name:
            # 文件不在同名文件夹中，需要创建文件夹并更新路径
            folder_path = os.path.join(dirname, folder_name)
            os.makedirs(folder_path, exist_ok=True)

            # 构建最终的文件路径
            data_filename = os.path.join(folder_path, f"{basename}_{device_id}_brightness_data.txt")
            raw_filename = os.path.join(folder_path, f"{basename}_{device_id}_raw_logcat.txt")

            print(f"创建文件夹: {folder_path}")
        else:
            # 文件已在同名文件夹中，直接使用原路径
            data_filename = os.path.join(dirname, f"{basename}_{device_id}_brightness_data.txt")
            raw_filename = os.path.join(dirname, f"{basename}_{device_id}_raw_logcat.txt")

            print(f"使用现有文件夹: {dirname}")

        return folder_name, data_filename, raw_filename

    def disable_data_logging(self):
        """
        禁用数据记录功能并关闭所有文件
        """
        if not self.data_logging_enabled:
            return

        self.data_logging_enabled = False

        with self.file_lock:
            # 关闭所有数据文件
            for device_id, file_handle in self.data_files.items():
                try:
                    file_handle.close()
                    print(f"已关闭设备 {device_id} 的数据文件")
                except Exception as e:
                    print(f"关闭设备 {device_id} 数据文件失败: {e}")

            # 关闭所有原始数据文件
            for device_id, file_handle in self.raw_files.items():
                try:
                    file_handle.close()
                    print(f"已关闭设备 {device_id} 的原始数据文件")
                except Exception as e:
                    print(f"关闭设备 {device_id} 原始数据文件失败: {e}")

            # 清空文件句柄字典
            self.data_files.clear()
            self.raw_files.clear()

    def set_current_scene(self, scene_name):
        """
        设置当前测试场景

        Args:
            scene_name (str): 场景名称
        """
        self.current_scene = scene_name

    def _write_raw_data(self, device_id, log_line):
        """
        写入原始logcat数据

        Args:
            device_id (str): 设备ID
            log_line (str): 原始日志行
        """
        try:
            with self.file_lock:
                if device_id in self.raw_files:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    self.raw_files[device_id].write(f"{timestamp}: {log_line}")
                    self.raw_files[device_id].flush()  # 立即写入磁盘
        except Exception as e:
            print(f"写入设备 {device_id} 原始数据失败: {e}")

    def _write_processed_data(self, device_id, target_nit, current_nit, drop_percent):
        """
        写入处理后的数据

        Args:
            device_id (str): 设备ID
            target_nit (str): 目标亮度值
            current_nit (str): 当前亮度值
            drop_percent (str): 降幅百分比
        """
        try:
            with self.file_lock:
                if device_id in self.data_files:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    data_line = f"{timestamp},{self.current_scene},{target_nit},{current_nit},{drop_percent}\n"
                    self.data_files[device_id].write(data_line)
                    self.data_files[device_id].flush()  # 立即写入磁盘
        except Exception as e:
            print(f"写入设备 {device_id} 处理数据失败: {e}")