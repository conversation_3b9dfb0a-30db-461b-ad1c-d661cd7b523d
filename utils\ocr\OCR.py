import ctypes
from bs4 import BeautifulSoup
import re

class OCR():

    def __init__(self, DLL_PATH, TESSDATA_PREFIX, lang):
        self.DLL_PATH = DLL_PATH
        self.TESSDATA_PREFIX = TESSDATA_PREFIX
        self.lang = lang
        self.ready = False
        if self.do_init():
            self.ready = True

    def do_init(self):
        self.tesseract = ctypes.cdll.LoadLibrary(self.DLL_PATH)
        self.tesseract.TessBaseAPICreate.restype = ctypes.c_uint64
        self.api = self.tesseract.TessBaseAPICreate()
        rc = self.tesseract.TessBaseAPIInit3(ctypes.c_uint64(self.api), self.TESSDATA_PREFIX, self.lang)
        if rc:
            self.tesseract.TessBaseAPIDelete(ctypes.c_uint64(self.api))
            print('Could not initialize tesseract.\n')
            return False
        return True

    def get_text(self, path):
        if not self.ready:
            return False
        self.tesseract.TessBaseAPIProcessPages(
            ctypes.c_uint64(self.api), path, None, 0, None)
        self.tesseract.TessBaseAPIGetUTF8Text.restype = ctypes.c_uint64
        text_out = self.tesseract.TessBaseAPIGetUTF8Text(ctypes.c_uint64(self.api))
        return bytes.decode(ctypes.string_at(text_out)).strip()

    def get_text_and_positions(self, path):
        if not self.ready:
            return False

        # 处理页面，并获取HOCR格式的结果，0代表第一页，通常通过HOCR可以获取每个文字的位置信息
        self.tesseract.TessBaseAPIProcessPages(
            ctypes.c_uint64(self.api), path, None, 0, None)

        # 获取HOCR文本的函数可能需要设置正确的返回类型
        self.tesseract.TessBaseAPIGetHOCRText.restype = ctypes.c_uint64

        # 这里的1表示我们在要求tesseract返回HOCR格式的数据
        hocr_out = self.tesseract.TessBaseAPIGetHOCRText(ctypes.c_uint64(self.api), 1)

        # 将输出转换为python字符串
        hocr_string = bytes.decode(ctypes.string_at(hocr_out)).strip()

        # 这里，你需要解析HOCR字符串以提取文字和位置。
        # 你可以使用HTML解析器，像BeautifulSoup来查找每个<bbox>标签，并获取它的位置和文字。

        # 返回解析得到的文字及其位置信息，可能是一个列表或字典。
        # 例如: [{'text': 'example', 'positions': (x1, y1, x2, y2)}, ...]
        return self.parse_ocr_data(hocr_string)

    def parse_ocr_data(self, html_content):
        soup = BeautifulSoup(html_content, 'html.parser')
        ocr_data = []

        # Find all word elements with OCR data
        words = soup.find_all('span', class_='ocrx_word')

        # Process each word
        for word in words:
            title = word['title']
            bbox_match = re.search(r'bbox (\d+) (\d+) (\d+) (\d+)', title)
            confidence_match = re.search(r'x_wconf (\d+)', title)

            if bbox_match and confidence_match:
                # Extract bbox and confidence
                bbox = tuple(map(int, bbox_match.groups()))
                confidence = int(confidence_match.group(1))
                # Extract text
                text = word.get_text()
                # Create dictionary with OCR data
                ocr_dict = {
                    'bbox': bbox,
                    'text': text,
                    'confidence': confidence
                }
                ocr_data.append(ocr_dict)

        return ocr_data
