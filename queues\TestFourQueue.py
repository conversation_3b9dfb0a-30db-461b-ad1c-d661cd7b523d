import datetime
import math
import threading
import time

import config
from enums.PackageName import PackageName
from utils import ExU2
from utils.adb.OpenApp import OpenApp
from utils.adb.ExAdb import ExAdb
from utils.GetBrightnessReduction import GetBrightnessReduction
from utils.ExcelSave import ExcelSave
from utils.Util import Util
from utils.AppConfig import AppConfig
from utils.PresetsConfig import PresetsConfig


class TestFourQueue:
    """ 测试队列 """
    def __init__(self):
        self.queue = []
        self.queue_title = None
        self.brightnessModel = GetBrightnessReduction()
        self.adb = ExAdb()
        # 创建AppConfig实例
        self.app_config = AppConfig(config_path="app_config.json", version="4.0")
        # 兼容旧版本，保留package_dict和package_key
        self.package_dict = {item.value.get("desc"): item.value.get("package") for item in PackageName}
        self.package_key = {item.value.get("desc"): item.name for item in PackageName}
        self.config_dict = config.get_4_config()
        self.test_standard = "indoor"  # 测试标准 indoor（室内）/outdoor（室外）
        self.res_dict = {}
        self.is_auto_brightness = False
        self.file_info = "A"
        self.logs = None
        self.is_start_brightness_thread = False
        self.nit_arr_int = []
        self.file_name = None
        # 当前需要测试的nit亮度值
        self.current_nit_brightness_value = None
        self.util = Util()
        # 进度条组件实例
        self.progress_bar = None
        # 创建OpenApp实例，用于获取包名
        self.open_app = OpenApp()

    def init_app(self):
        """ 初始化 """
        self.adb.get_root()
        time.sleep(5)

    def start_brightness_thread(self):
        """ 启动亮度线程 """
        if self.brightnessModel.is_running():
            self.brightnessModel.stop_threads()
            self.brightnessModel.start_threads()
        else:
            self.brightnessModel.start_threads()
            self.is_start_brightness_thread = True

    def stop_brightness_thread(self):
        """ 停止亮度线程 """
        self.brightnessModel.stop_threads()
        self.is_start_brightness_thread = False


    def get_test_standard(self):
        return self.test_standard

    def set_test_standard(self, value):
        self.test_standard = value

    def get_config_dict(self):
        return self.config_dict

    def set_config_dict(self, config_dict):
        self.config_dict = config_dict

    def get_current_time(self):
        # 获取当前时间
        now = datetime.datetime.now()
        formatted_datetime = now.strftime('%Y-%m-%d %H-%M-%S')
        return formatted_datetime

    def set_file_name(self, file_name):
        self.file_name = file_name

    def get_file_name(self):
        return self.file_name

    def get_new_file_name(self, device_id, formatted_datetime):
        """ 获取新的文件名称 """
        # 在文件名中添加亮度值信息
        nit_info = f"_{self.current_nit_brightness_value}nit" if self.current_nit_brightness_value else ""
        return str(self.get_device_name(device_id)) + "_" + str(formatted_datetime) + nit_info + "_" + self.get_queue_title() + ".xlsx"

    def get_nit_arr_int(self):
        """ 获取nit数组 """
        return self.nit_arr_int

    def set_nit_arr_int(self, nit_arr_int):
        """ 设置nit数组 """
        self.nit_arr_int = nit_arr_int

    def get_is_start_brightness_thread(self):
        """ 获取亮度线程状态 """
        return self.is_start_brightness_thread

    def set_logs(self, logs):
        self.logs = logs
        # 设置OpenApp实例的logs
        self.open_app.logs = logs

    def set_file_info(self, file_info):
        self.file_info = file_info

    def get_queue_title(self):
        """ 获取队列标题 """
        if not self.queue_title:
            self.queue_title = "测试结果"
        return self.queue_title

    def set_queue_title(self, title):
        """ 设置队列标题 """
        self.queue_title = title

    def set_progress_bar(self, progress_bar):
        """ 设置进度条 """
        self.progress_bar = progress_bar

    def set_is_auto_brightness(self, is_auto_brightness):
        """ 设置是否自动亮度 """
        self.is_auto_brightness = is_auto_brightness

    def get_is_auto_brightness(self):
        """ 获取是否自动亮度 """
        return self.is_auto_brightness

    def get_device_name(self, device_id):
        """ 获取设备名称 """
        device_name = self.adb.get_device_name(device_id)
        cleaned_device_name = device_name.replace('\n', '').replace('\r', '')
        return cleaned_device_name

    def get_nit_value(self, device_id):
        """ 获取nit值 """
        return self.brightnessModel.get_current_nit_value(device_id)

    def get_absolute_nit_value(self, device_id):
        """ 获取nit值 不受降幅影响的nit值 """
        return self.brightnessModel.get_current_absolute_nit_value(device_id=device_id)

    def get_drop_value(self, device_id):
        """ 获取亮度降幅 """
        return self.brightnessModel.get_current_brightness_drop(device_id)

    def get_package_name(self, desc):
        """ 根据描述 获取包名 """
        if "." in desc:
            return desc
        # 优先使用AppConfig获取包名
        package = self.app_config.get_app_package(desc)
        if package:
            return package
        # 兼容旧版本，使用package_dict
        return self.package_dict.get(desc)

    def set_queue_data(self, data):
        """ 设置队列数据 """
        self.queue = data

    def set_schedule(self, current=0, target=10):
        """ 设置进度条进度 """
        if current > 100:
            current = 100
        for i in range(current, target, 1):
            time.sleep(0.1)
            self.progress_bar.set(current)

    def calculate_task_length(self):
        """ 计算任务长度 """
        num = 100 / len(self.queue)
        result = math.ceil(num)
        return result

    def device_init(self, device_id):
        """ 设备初始化 """
        # 获取root权限
        self.adb.get_root()
        time.sleep(3)
        # 唤醒屏幕
        self.adb.wake_up_screen()
        time.sleep(3)
        # 自动亮度
        if self.is_auto_brightness:
            self.adb.start_auto_brightness()
        else:
            self.adb.close_auto_brightness()
        # 等待亮度稳定
        time.sleep(5)

    def start_queue(self, device_id, sheet_name=None, add_separator=False, is_headers=False):
        """ 开始测试队列 """
        print("初始化设备"+device_id)
        open_app = OpenApp()
        self.logs.info("初始化设备"+device_id)
        self.device_init(device_id=device_id)
        # 返回桌面
        open_app.open_desktop()
        # 查找最相近的nit亮度
        current_brightness_value = self.util.find_optimal_dbv(target_nit_value=self.current_nit_brightness_value, device_id=device_id)
        # 设置亮度
        self.adb.switch_brightness_255(brightness=current_brightness_value, device_id=device_id)
        # 等待亮度稳定
        time.sleep(3)
        open_app.set_device_id(device_id)
        # 设置应用配置
        open_app.set_app_config(self.app_config)

        # 启用数据记录功能
        if hasattr(self, 'brightnessModel') and self.brightnessModel:
            base_filename = self.get_file_name()
            if base_filename:
                # 移除.xlsx扩展名，使用基础文件名
                base_filename = base_filename.replace('.xlsx', '')
            self.brightnessModel.enable_data_logging(base_filename)

        # 使用sheet_name参数创建ExcelSave实例
        if self.get_file_name() is None:
            self.set_file_name(self.get_new_file_name(device_id, self.get_current_time()))
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name=sheet_name)
        else:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name=sheet_name)

        # 创建ExU2实例
        u2_helper = ExU2(device_id=device_id)

        data = {}
        length_value = self.calculate_task_length()
        current_value = 0
        for desc in self.queue:
            print(f"设备Id:{device_id}, 当前场景:{desc}")
            self.logs.info(f"设备Id:{device_id}, 当前场景:{desc}")

            # 更新当前场景信息到亮度数据记录器
            if hasattr(self, 'brightnessModel') and self.brightnessModel:
                # 为4.0版本添加亮度值信息到场景名称
                scene_with_brightness = f"{desc}({self.current_nit_brightness_value}nit)"
                self.brightnessModel.set_current_scene(scene_with_brightness)

            if "微信视频" in desc:
                open_app.open_weixin_video()
            elif "负一屏" in desc:
                open_app.open_negative_one_screen()
            elif "系统桌面" in desc:
                open_app.open_desktop()
            else:
                open_app.open_app(desc)
            # 调整亮度
            self.adb.switch_brightness_255(10, device_id)
            time.sleep(3)

            if u2_helper.contains_text("选择一个账号登录"):
                u2_helper.perform_action_on_mismatch()
                time.sleep(3)
            if u2_helper.contains_text("密码管理工具"):
                u2_helper.perform_action_on_mismatch(offset_y=600)

            # 目标亮度
            self.adb.switch_brightness_255(current_brightness_value, device_id)
            time.sleep(5)
            self.set_schedule(current_value, current_value+length_value)
            current_value += length_value
            current_nit_value = self.get_nit_value(device_id)
            drop_value = self.get_drop_value(device_id)
            nit_absolute_value = self.get_absolute_nit_value(device_id)
            check_value, standard_value = self.is_test_result(current_nit_value=current_nit_value, desc=desc, nit_absolute_value=nit_absolute_value, drop_current_value=drop_value)

            data[desc] = {"nit": current_nit_value, "drop": self.get_drop_value(device_id), "standard": standard_value, "result": check_value}
            if "微信视频" in desc:
                # 关闭当前app
                self.adb.stop_current_app(device_id)

        # 持久化，传递add_separator参数，但不添加元数据（is_final=False）
        excel_model.append_dict_to_excel(data=data, current_nit=self.current_nit_brightness_value, is_headers=is_headers, add_separator=add_separator, start_row=5)
        excel_model.save(self.get_file_name())

    def start_test_threads(self, sheet_name=None, add_separator=False, is_headers=False):
        """ 启动测试线程 """
        # 开启亮度线程
        self.start_brightness_thread()
        if self.current_nit_brightness_value:
            """ current_nit_brightness_value不为空证明是使用预设队列 """
            if not self.is_start_brightness_thread:
                return
            device_list = self.adb.get_device_list()
            self.logs.info(f"启动线程的设备列表:{device_list}")
            threads = []
            # self.start_queue(device_list[0])
            for device_id in device_list:
                thread = threading.Thread(target=self.start_queue, args=(device_id,), kwargs={"sheet_name": sheet_name, "add_separator": add_separator, "is_headers": is_headers})
                thread.daemon = True
                threads.append(thread)
                thread.start()
            #
            # # 等待所有线程完成
            for thread in threads:
                thread.join()
            self.logs.info(f"结束线程的设备列表:{device_list}")
        else:
            """ 为空则是自定义的队列 """
            for item in self.get_nit_arr_int():
                self.current_nit_brightness_value = item
                if not self.is_start_brightness_thread:
                    return
                device_list = self.adb.get_device_list()
                self.logs.info(f"启动线程的设备列表:{device_list}")
                threads = []
                # self.start_queue(device_list[0])
                for device_id in device_list:
                    thread = threading.Thread(target=self.start_queue, args=(device_id,), kwargs={"sheet_name": sheet_name, "add_separator": add_separator})
                    thread.daemon = True
                    threads.append(thread)
                    thread.start()
                #
                # # 等待所有线程完成
                for thread in threads:
                    thread.join()
                self.logs.info(f"结束线程的设备列表:{device_list}")
        self.stop_brightness_thread()

    def auto_and_manual_test(self):
        """ 自动和手动亮度降幅测试 """
        # 启动亮度线程
        self.start_brightness_thread()
        
        # 创建PresetsConfig实例
        presets_config = PresetsConfig(version="4.0")
        
        # 重置文件名，确保使用新的文件名
        self.set_file_name(None)
        
        # 获取设备列表，用于生成文件名
        device_list = self.adb.get_device_list()
        if len(device_list) < 1:
            return
            
        # 生成一个基础文件名，用于整个测试过程
        # 不包含亮度值，因为所有亮度值的数据都保存在同一个文件中
        base_file_name = str(self.get_device_name(device_list[0])) + "_" + self.get_current_time() + "_" + self.get_queue_title() + ".xlsx"
        self.set_file_name(base_file_name)
        
        # 处理自动亮度测试
        # 从预设配置文件中获取应用列表
        preset_auto_list = presets_config.get_preset_apps("auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_auto_list:
            auto_list = preset_auto_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            auto_list_dict = {}
            for app_item in PackageName.get_package_auto_and_manua_list_4_0():
                app_desc = app_item.value.get("desc")
                auto_list_dict[app_desc] = True
            auto_list = list(auto_list_dict.keys())
        
        # 设置队列数据
        self.set_queue_data(auto_list)
        self.set_is_auto_brightness(True)
        self.set_file_info("自动亮度")
        
        # 为每个亮度值执行自动亮度测试，结果保存在同一个sheet中
        is_first_nit = True
        is_headers = False
        for item in self.get_nit_arr_int():
            self.current_nit_brightness_value = item
            # 只有第一个亮度值不需要添加分隔符
            self.start_test_threads(sheet_name="自动亮度", add_separator=True, is_headers=is_headers)
            is_headers = True
            is_first_nit = False

        # 在所有亮度值测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="自动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="自动亮度")
            # 保存文件后立即关闭，避免持续占用
            excel_model.save(self.get_file_name())
            break

        # 处理手动亮度测试
        # 从预设配置文件中获取应用列表
        preset_manual_list = presets_config.get_preset_apps("auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_manual_list:
            manual_list = preset_manual_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            manual_list_dict = {}
            for app_item in PackageName.get_package_auto_and_manua_list_4_0():
                app_desc = app_item.value.get("desc")
                manual_list_dict[app_desc] = True
            manual_list = list(manual_list_dict.keys())
        
        # 设置队列数据
        self.set_queue_data(manual_list)
        self.set_is_auto_brightness(False)
        self.set_file_info("手动亮度")
        
        # 为每个亮度值执行手动亮度测试，结果保存在同一个sheet中
        is_first_nit = True
        is_headers = False
        for item in self.get_nit_arr_int():
            self.current_nit_brightness_value = item
            # 只有第一个亮度值不需要添加分隔符
            self.start_test_threads(sheet_name="手动亮度", add_separator=True, is_headers=is_headers)
            is_headers = True
            is_first_nit = False
        
        # 在所有亮度值测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="手动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="手动亮度")
            # 保存文件并关闭
            excel_model.save(self.get_file_name())
            break
        
        self.stop_brightness_thread()

    def ex_auto_and_manual_test(self):
        """ 外销自动和手动亮度 """
        # 启动亮度线程
        self.start_brightness_thread()
        
        # 创建PresetsConfig实例
        presets_config = PresetsConfig(version="4.0")
        
        # 重置文件名，确保使用新的文件名
        self.set_file_name(None)
        
        # 获取设备列表，用于生成文件名
        device_list = self.adb.get_device_list()
        if len(device_list) < 1:
            return
            
        # 生成一个基础文件名，用于整个测试过程
        # 不包含亮度值，因为所有亮度值的数据都保存在同一个文件中
        base_file_name = str(self.get_device_name(device_list[0])) + "_" + self.get_current_time() + "_外销_" + self.get_queue_title() + ".xlsx"
        self.set_file_name(base_file_name)
        
        # 处理外销自动亮度测试
        # 从预设配置文件中获取应用列表
        preset_ex_auto_list = presets_config.get_preset_apps("ex_auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_ex_auto_list:
            ex_auto_list = preset_ex_auto_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            ex_auto_list_dict = {}
            for app_item in PackageName.get_package_auto_and_manua_list_4_0():
                app_desc = app_item.value.get("desc")
                ex_auto_list_dict[app_desc] = True
            ex_auto_list = list(ex_auto_list_dict.keys())
        
        # 设置队列数据
        self.set_queue_data(ex_auto_list)
        self.set_is_auto_brightness(True)
        self.set_file_info("外销自动亮度")
        
        # 为每个亮度值执行外销自动亮度测试，结果保存在同一个sheet中
        is_first_nit = True
        is_headers = False
        for item in self.get_nit_arr_int():
            self.current_nit_brightness_value = item
            # 只有第一个亮度值不需要添加分隔符
            self.start_test_threads(sheet_name="外销自动亮度", add_separator=True, is_headers=is_headers)
            is_headers = True
            is_first_nit = False
        
        # 在所有亮度值测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="外销自动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="外销自动亮度")
            # 保存文件后立即关闭，避免持续占用
            excel_model.save(self.get_file_name())
            break
        
        # 处理外销手动亮度测试
        # 从预设配置文件中获取应用列表
        preset_ex_manual_list = presets_config.get_preset_apps("ex_auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_ex_manual_list:
            ex_manual_list = preset_ex_manual_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            ex_manual_list_dict = {}
            for app_item in PackageName.get_package_auto_and_manua_list_4_0():
                app_desc = app_item.value.get("desc")
                ex_manual_list_dict[app_desc] = True
            ex_manual_list = list(ex_manual_list_dict.keys())
        
        # 设置队列数据
        self.set_queue_data(ex_manual_list)
        self.set_is_auto_brightness(False)
        self.set_file_info("外销手动亮度")
        
        # 为每个亮度值执行外销手动亮度测试，结果保存在同一个sheet中
        is_first_nit = True
        is_headers = False
        for item in self.get_nit_arr_int():
            self.current_nit_brightness_value = item
            # 只有第一个亮度值不需要添加分隔符
            self.start_test_threads(sheet_name="外销手动亮度", add_separator=True, is_headers=is_headers)
            is_headers = True
            is_first_nit = False
        
        # 在所有亮度值测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="外销手动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="外销手动亮度")
            # 保存文件并关闭
            excel_model.save(self.get_file_name())
            break
        
        self.stop_brightness_thread()

    def is_test_result(self, current_nit_value, desc, nit_absolute_value, drop_current_value):
        """ 判断测试结果 return bool(是否符合标准) string(标准值) """
        # 优先使用AppConfig获取标准值
        key = self.app_config.get_key_by_desc(desc)
        if key:
            # 使用AppConfig获取标准值
            is_indoor = self.get_test_standard() == "indoor"
            is_auto = self.get_is_auto_brightness()
            
            # 4.0版本需要考虑不同亮度值的标准
            app_info = self.app_config.get_app_by_desc(desc)
            if app_info:
                standards = app_info.get("standards", {}).get("4.0", {})
                if is_indoor:
                    drop_values = standards.get("indoor", {})
                else:
                    drop_values = standards.get("outdoor", {})
                
                # 获取当前亮度值对应的标准
                nit_key = str(self.current_nit_brightness_value)
                if isinstance(drop_values, dict) and nit_key in drop_values:
                    drop_value = drop_values.get(nit_key, "0/0")
                else:
                    drop_value = "0/0"
                
                # 根据自动/手动亮度选择标准
                if is_auto:
                    drop_value = drop_value.split("/")[0]
                else:
                    drop_value = drop_value.split("/")[1]
            else:
                drop_value = "0"
        else:
            # 兼容旧版本
            key = self.package_key.get(desc)
            if not key:
                return False, "0"
                
            config_dict = self.get_config_dict()
            config_data = config_dict.get(key)
            # 降幅标准值 indoor（室内）/ outdoor（室外）
            if self.get_test_standard() == "indoor":
                drop_value = config_data.get("indoor")
            else:
                drop_value = config_data.get("outdoor")

            # 取对应的亮度的标准值
            drop_value = drop_value.get(str(self.current_nit_brightness_value), 0)

            if self.get_is_auto_brightness():
                # 自动亮度
                drop_value = drop_value.split("/")[0]
            else:
                # 手动亮度
                drop_value = drop_value.split("/")[1]

        drop_str_value = str(drop_value)

        if "%" in drop_value:
            drop_value = drop_value.split("%")[0]
            # 降幅为0，直接判断当前亮度值是否等于绝对亮度值
            if float(drop_value) < 1:
                return float(drop_current_value) < 1, drop_str_value

            # 降幅不为0，判断当前亮度值是否在标准值的范围内
            if float(drop_value) - 3 <= float(drop_current_value) <= float(drop_value) + 3:
                return True, drop_str_value
        else:
            drop_value = drop_value.split("nit")[0]
            # 降幅为0，直接判断当前亮度值是否等于绝对亮度值
            if int(drop_value) == 0:
                return current_nit_value == nit_absolute_value, drop_str_value

            # 降幅不为0，判断当前亮度值是否在标准值的范围内
            if int(drop_value) - 5 <= float(current_nit_value) <= int(drop_value) + 5:
                return True, drop_str_value

        return False, drop_str_value
        
    def set_version(self, version):
        """设置标准版本 4.0 或 5.0"""
        self.app_config.set_version(version)
