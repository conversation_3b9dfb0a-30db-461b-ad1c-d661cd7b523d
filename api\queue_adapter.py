"""
队列操作适配器
提供API与现有QueueController之间的接口适配
"""

import threading
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from queues.TestQueue import TestQueue
from queues.TestFourQueue import TestFourQueue
from queues.TaskQueueManager import ExecutionStatus, QueueStatus
from utils.PresetsConfig import PresetsConfig
from enums.PackageName import PackageName


class QueueAdapter:
    """队列操作适配器，连接API和现有队列控制器"""
    
    def __init__(self, queue_controller=None, main_controller=None, four_controller=None):
        """
        初始化队列适配器

        Args:
            queue_controller: QueueController实例
            main_controller: MainController实例（5.0版本）
            four_controller: FourController实例（4.0版本）
        """
        self.queue_controller = queue_controller
        self.main_controller = main_controller
        self.four_controller = four_controller
        self._lock = threading.Lock()

        # 初始化预设配置管理器
        self.presets_config_5_0 = PresetsConfig(version="5.0")
        self.presets_config_4_0 = PresetsConfig(version="4.0")
    
    def set_controllers(self, queue_controller=None, main_controller=None, four_controller=None):
        """设置控制器引用"""
        if queue_controller:
            self.queue_controller = queue_controller
        if main_controller:
            self.main_controller = main_controller
        if four_controller:
            self.four_controller = four_controller

    def _get_apps_from_preset_5_0(self, preset_name: str) -> tuple[List[str], bool]:
        """
        根据5.0版本预设名称获取应用列表和自动亮度设置

        Args:
            preset_name: 预设名称

        Returns:
            tuple: (应用列表, 是否自动亮度)
        """
        # 预设名称到预设键的映射
        preset_key_map = {
            "内_OLED_自动": ("内_OLED_自动", True),
            "内_OLED_手动": ("内_OLED_手动", False),
            "外_OLED_自动": ("外_OLED_自动", True),
            "外_OLED_手动": ("外_OLED_手动", False),
            "内_LCD_自动": ("内_LCD_自动", True),
            "内_LCD_手动": ("内_LCD_手动", False),
            "外_LCD_自动": ("外_LCD_自动", True),
            "外_LCD_手动": ("外_LCD_手动", False),
            # 向下兼容旧的预设名称
            "自动": ("auto", True),
            "手动": ("manual", False),
            "外销自动": ("ex_auto", True),
            "外销手动": ("ex_manual", False)
        }

        if preset_name not in preset_key_map:
            return [], False

        preset_key, auto_brightness = preset_key_map[preset_name]

        # 从预设配置获取应用列表
        app_list = self.presets_config_5_0.get_preset_apps(preset_key)

        if app_list:
            return app_list, auto_brightness

        # 如果预设配置中没有，使用fallback列表
        fallback_map = {
            "内_OLED_自动": PackageName.get_package_auto_list(),
            "内_OLED_手动": PackageName.get_package_manual_list(),
            "外_OLED_自动": PackageName.get_package_ex_auto_list(),
            "外_OLED_手动": PackageName.get_package_ex_manual_list(),
            "内_LCD_自动": PackageName.get_package_auto_list(),
            "内_LCD_手动": PackageName.get_package_manual_list(),
            "外_LCD_自动": PackageName.get_package_ex_auto_list(),
            "外_LCD_手动": PackageName.get_package_ex_manual_list(),
            "自动": PackageName.get_package_auto_list(),
            "手动": PackageName.get_package_manual_list(),
            "外销自动": PackageName.get_package_ex_auto_list(),
            "外销手动": PackageName.get_package_ex_manual_list()
        }

        fallback_packages = fallback_map.get(preset_name, [])
        fallback_apps = [item.value.get("desc") for item in fallback_packages]

        return fallback_apps, auto_brightness

    def _get_apps_from_preset_4_0(self, preset_name: str) -> tuple[List[str], bool]:
        """
        根据4.0版本预设名称获取应用列表和自动亮度设置

        Args:
            preset_name: 预设名称

        Returns:
            tuple: (应用列表, 是否自动亮度)
        """
        # 预设名称到预设键的映射
        preset_key_map = {
            "内销自动": ("auto_manual", True),
            "内销手动": ("manual", False),
            "外销自动": ("ex_auto_manual", True),
            "外销手动": ("ex_manual", False),
            # 向下兼容
            "自动": ("auto_manual", True),
            "手动": ("manual", False),
            "外销自动": ("ex_auto_manual", True),
            "外销手动": ("ex_manual", False)
        }

        if preset_name not in preset_key_map:
            return [], False

        preset_key, auto_brightness = preset_key_map[preset_name]

        # 从预设配置获取应用列表
        app_list = self.presets_config_4_0.get_preset_apps(preset_key)

        if app_list:
            return app_list, auto_brightness

        # 如果预设配置中没有，使用fallback列表
        fallback_map = {
            "内销自动": PackageName.get_package_auto_list(),
            "内销手动": PackageName.get_package_manual_list(),
            "外销自动": PackageName.get_package_ex_auto_list(),
            "外销手动": PackageName.get_package_ex_manual_list(),
            "自动": PackageName.get_package_auto_list(),
            "手动": PackageName.get_package_manual_list()
        }

        fallback_packages = fallback_map.get(preset_name, [])
        fallback_apps = [item.value.get("desc") for item in fallback_packages]

        return fallback_apps, auto_brightness
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态（优化版本，减少锁使用）"""
        # 快速检查控制器是否初始化（无需锁）
        if not self.queue_controller:
            return {"error": "队列控制器未初始化"}

        try:
            # 获取队列管理器引用（无需锁）
            queue_manager = self.queue_controller.queue_manager

            # 快速获取状态信息（这些操作应该是非阻塞的）
            return {
                "queue_count": queue_manager.get_queue_count(),
                "is_running": queue_manager.is_running,
                "current_queue_index": queue_manager.current_queue_index,
                "queue_titles": queue_manager.get_queue_titles(),
                "is_empty": queue_manager.is_empty()
            }
        except Exception as e:
            return {"error": f"获取队列状态失败: {str(e)}"}
    
    def get_queue_list(self) -> List[Dict[str, Any]]:
        """获取详细的队列列表"""
        with self._lock:
            if not self.queue_controller:
                return []
            
            try:
                queue_manager = self.queue_controller.queue_manager
                queues_info = []
                
                for i, queue in enumerate(queue_manager.queues):
                    queue_info = {
                        "index": i,
                        "title": queue.get_queue_title(),
                        "type": "5.0" if isinstance(queue, TestQueue) else "4.0",
                        "apps": getattr(queue, 'queue', []),
                        "is_auto_brightness": getattr(queue, 'is_auto_brightness', False)
                    }
                    
                    # 如果是4.0版本队列，添加亮度信息
                    if isinstance(queue, TestFourQueue):
                        queue_info["brightness_values"] = getattr(queue, 'nit_arr_int', [])
                    
                    queues_info.append(queue_info)
                
                return queues_info
            except Exception as e:
                return [{"error": f"获取队列列表失败: {str(e)}"}]
    
    def add_queue_5_0(self, apps: List[str] = None, preset: str = None, auto_brightness: bool = None) -> Dict[str, Any]:
        """
        添加5.0版本队列

        Args:
            apps: 应用列表（可选，如果提供preset则可为空）
            preset: 预设名称（可选）
            auto_brightness: 是否自动亮度（可选，如果提供preset则自动设置）
        """
        with self._lock:
            if not self.main_controller or not self.queue_controller:
                return {"success": False, "message": "控制器未初始化"}

            try:
                final_apps = apps or []
                final_auto_brightness = auto_brightness if auto_brightness is not None else False

                # 如果提供了预设名称，从预设获取应用列表和亮度设置
                if preset:
                    preset_apps, preset_auto_brightness = self._get_apps_from_preset_5_0(preset)
                    if preset_apps:
                        final_apps = preset_apps
                        # 如果没有明确指定auto_brightness，使用预设的设置
                        if auto_brightness is None:
                            final_auto_brightness = preset_auto_brightness
                    else:
                        return {"success": False, "message": f"未找到预设: {preset}"}

                # 检查最终的应用列表
                if not final_apps:
                    return {"success": False, "message": "应用列表不能为空，请提供apps参数或有效的preset参数"}

                # 创建TestQueue实例
                test_queue = TestQueue()
                test_queue.set_logs(self.main_controller.logs)
                test_queue.set_queue_data(final_apps)
                test_queue.set_is_auto_brightness(final_auto_brightness)

                # 设置队列标题
                title = f"5.0 - {preset}" if preset else f"5.0 - {'自动亮度' if final_auto_brightness else '手动亮度'}"
                test_queue.set_queue_title(title)

                # 添加到执行队列
                success = self.queue_controller.add_to_execution_queue(test_queue)

                if success:
                    return {
                        "success": True,
                        "message": f"已添加队列: {title}",
                        "queue_title": title,
                        "apps": final_apps,
                        "auto_brightness": final_auto_brightness
                    }
                else:
                    return {"success": False, "message": "添加队列失败"}

            except Exception as e:
                return {"success": False, "message": f"添加5.0队列失败: {str(e)}"}
    
    def add_queue_4_0(self, apps: List[str] = None, brightness_values: List[int] = None, preset: str = None, auto_brightness: bool = None) -> Dict[str, Any]:
        """
        添加4.0版本队列

        Args:
            apps: 应用列表（可选，如果提供preset则可为空）
            brightness_values: 亮度值列表（必需）
            preset: 预设名称（可选）
            auto_brightness: 是否自动亮度（可选，如果提供preset则自动设置）
        """
        with self._lock:
            if not self.four_controller or not self.queue_controller:
                return {"success": False, "message": "控制器未初始化"}

            try:
                # 亮度值列表是必需的
                if not brightness_values:
                    return {"success": False, "message": "亮度值列表不能为空"}

                final_apps = apps or []
                final_auto_brightness = auto_brightness if auto_brightness is not None else False

                # 如果提供了预设名称，从预设获取应用列表和亮度设置
                if preset:
                    preset_apps, preset_auto_brightness = self._get_apps_from_preset_4_0(preset)
                    if preset_apps:
                        final_apps = preset_apps
                        # 如果没有明确指定auto_brightness，使用预设的设置
                        if auto_brightness is None:
                            final_auto_brightness = preset_auto_brightness
                    else:
                        return {"success": False, "message": f"未找到预设: {preset}"}

                # 检查最终的应用列表
                if not final_apps:
                    return {"success": False, "message": "应用列表不能为空，请提供apps参数或有效的preset参数"}

                # 创建TestFourQueue实例
                test_queue = TestFourQueue()
                test_queue.set_logs(self.four_controller.logs)
                test_queue.set_queue_data(final_apps)
                test_queue.set_is_auto_brightness(final_auto_brightness)
                test_queue.set_nit_arr_int(brightness_values)

                # 设置队列标题
                title = f"4.0 - {preset}" if preset else f"4.0 - {'自动亮度' if final_auto_brightness else '手动亮度'}"
                test_queue.set_queue_title(title)

                # 添加到执行队列
                success = self.queue_controller.add_to_execution_queue(test_queue)

                if success:
                    return {
                        "success": True,
                        "message": f"已添加队列: {title}",
                        "queue_title": title,
                        "apps": final_apps,
                        "auto_brightness": final_auto_brightness,
                        "brightness_values": brightness_values
                    }
                else:
                    return {"success": False, "message": "添加队列失败"}

            except Exception as e:
                return {"success": False, "message": f"添加4.0队列失败: {str(e)}"}
    
    def start_queue_execution(self, device_id: str = None) -> Dict[str, Any]:
        """启动队列执行（API异步调用）"""
        with self._lock:
            if not self.queue_controller:
                return {"success": False, "message": "队列控制器未初始化"}

            try:
                # 如果没有指定设备ID，尝试获取默认设备
                if not device_id:
                    device_id = self.queue_controller._get_device_id()
                    if not device_id:
                        return {"success": False, "message": "未找到连接的设备"}

                # 在新线程中启动队列执行，避免阻塞API响应
                def execute_in_thread():
                    try:
                        self.queue_controller.execute_queues()
                    except Exception as e:
                        # 记录执行错误，但不影响API响应
                        if hasattr(self.queue_controller, 'logs') and self.queue_controller.logs:
                            self.queue_controller.logs.error(f"队列执行异常: {str(e)}")

                execution_thread = threading.Thread(
                    target=execute_in_thread,
                    daemon=True
                )
                execution_thread.start()

                # 立即返回成功响应
                return {
                    "success": True,
                    "message": "队列执行已启动",
                    "device_id": device_id
                }

            except Exception as e:
                return {"success": False, "message": f"启动队列执行失败: {str(e)}"}
    
    def stop_queue_execution(self) -> Dict[str, Any]:
        """停止队列执行"""
        with self._lock:
            if not self.queue_controller:
                return {"success": False, "message": "队列控制器未初始化"}
            
            try:
                self.queue_controller.stop_execution()
                return {"success": True, "message": "队列执行已停止"}
            except Exception as e:
                return {"success": False, "message": f"停止队列执行失败: {str(e)}"}
    
    def clear_queue(self) -> Dict[str, Any]:
        """清空队列"""
        with self._lock:
            if not self.queue_controller:
                return {"success": False, "message": "队列控制器未初始化"}
            
            try:
                self.queue_controller.clear_execution_queue()
                return {"success": True, "message": "队列已清空"}
            except Exception as e:
                return {"success": False, "message": f"清空队列失败: {str(e)}"}
    
    def get_device_list(self) -> List[str]:
        """获取设备列表"""
        try:
            if self.queue_controller and hasattr(self.queue_controller, 'adb'):
                return self.queue_controller.adb.get_device_list()
            elif self.main_controller and hasattr(self.main_controller, 'adb'):
                return self.main_controller.adb.get_device_list()
            else:
                return []
        except Exception:
            return []
    
    def is_device_connected(self) -> bool:
        """检查是否有设备连接"""
        device_list = self.get_device_list()
        return len(device_list) > 0

    def get_available_presets(self) -> Dict[str, List[str]]:
        """获取可用的预设列表"""
        try:
            # 5.0版本预设
            presets_5_0 = [
                "内_OLED_自动", "内_OLED_手动",
                "外_OLED_自动", "外_OLED_手动",
                "内_LCD_自动", "内_LCD_手动",
                "外_LCD_自动", "外_LCD_手动"
            ]

            # 4.0版本预设
            presets_4_0 = [
                "内销自动", "内销手动",
                "外销自动", "外销手动"
            ]

            return {
                "5.0": presets_5_0,
                "4.0": presets_4_0
            }
        except Exception:
            return {"5.0": [], "4.0": []}

    def get_execution_status(self) -> Dict[str, Any]:
        """获取详细的执行状态信息（优化版本，减少阻塞）"""
        # 快速检查控制器是否初始化（无需锁）
        if not self.queue_controller:
            return {"error": "队列控制器未初始化"}

        try:
            # 获取队列管理器引用（无需锁）
            queue_manager = self.queue_controller.queue_manager

            # 快速获取基本状态信息（这些操作应该是非阻塞的）
            summary = queue_manager.get_execution_summary()
            queue_statuses = queue_manager.get_queue_statuses()
            queue_titles = queue_manager.get_queue_titles()
            current_index = queue_manager.current_queue_index

            # 构建详细队列信息
            detailed_queues = []
            for i, (title, status) in enumerate(zip(queue_titles, queue_statuses)):
                detailed_queues.append({
                    "index": i,
                    "title": title,
                    "status": status.value,
                    "is_current": i == current_index
                })

            return {
                "execution_summary": summary,
                "detailed_queues": detailed_queues,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            # 返回基本错误信息，避免阻塞
            return {
                "error": f"获取执行状态失败: {str(e)}",
                "execution_summary": {
                    "execution_status": "error",
                    "total_queues": 0,
                    "completed_queues": 0,
                    "failed_queues": 0,
                    "current_queue_index": -1,
                    "is_running": False,
                    "error_count": 1
                },
                "detailed_queues": [],
                "timestamp": datetime.now().isoformat()
            }

    def get_execution_errors(self) -> Dict[str, Any]:
        """获取执行错误信息（优化版本，减少阻塞）"""
        # 快速检查控制器是否初始化（无需锁）
        if not self.queue_controller:
            return {"error": "队列控制器未初始化"}

        try:
            # 获取队列管理器引用（无需锁）
            queue_manager = self.queue_controller.queue_manager

            # 快速获取错误信息（这些操作应该是非阻塞的）
            errors = queue_manager.get_execution_errors()
            last_error = queue_manager.get_last_error()

            return {
                "error_count": len(errors),
                "errors": errors,
                "last_error": last_error,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"error": f"获取错误信息失败: {str(e)}"}

    def is_execution_completed(self) -> bool:
        """检查执行是否已完成"""
        try:
            if not self.queue_controller:
                return False

            queue_manager = self.queue_controller.queue_manager
            return queue_manager.is_completed()

        except Exception:
            return False

    def is_execution_failed(self) -> bool:
        """检查执行是否失败"""
        try:
            if not self.queue_controller:
                return False

            queue_manager = self.queue_controller.queue_manager
            return queue_manager.is_failed()

        except Exception:
            return False
