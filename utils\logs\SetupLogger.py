import logging


def setup_logger(log_file='app.log', log_level=logging.INFO):
    """
    设置日志记录器。

    :param log_file: 日志文件名，默认是'app.log'
    :param log_level: 日志级别，默认是logging.INFO
    :return: 配置好的Logger对象
    """
    # 创建Logger对象
    logger = logging.getLogger(__name__)
    logger.setLevel(log_level)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(log_level)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)

    # 定义日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到Logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger
