"""
自定义异常类

定义了客户端库使用的所有异常类型，提供详细的错误信息和处理建议。
"""

from typing import Optional, Dict, Any


class BrightnessClientError(Exception):
    """
    客户端库基础异常类
    
    所有其他异常类的基类，提供通用的错误处理功能。
    """
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            details: 详细错误信息字典
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.details:
            return f"{self.message} - 详细信息: {self.details}"
        return self.message


class APIConnectionError(BrightnessClientError):
    """
    API连接错误
    
    当无法连接到API服务器时抛出此异常。
    可能的原因：
    - API服务未启动
    - 网络连接问题
    - 端口被占用
    """
    
    def __init__(self, message: str = "无法连接到API服务器", url: Optional[str] = None):
        details = {"url": url} if url else {}
        super().__init__(message, details)


class APIRequestError(BrightnessClientError):
    """
    API请求错误
    
    当API请求失败时抛出此异常。
    包含HTTP状态码和响应信息。
    """
    
    def __init__(self, message: str, status_code: Optional[int] = None, 
                 response_data: Optional[Dict[str, Any]] = None):
        details = {}
        if status_code:
            details["status_code"] = status_code
        if response_data:
            details["response"] = response_data
        
        super().__init__(message, details)
        self.status_code = status_code
        self.response_data = response_data


class ServiceUnavailableError(BrightnessClientError):
    """
    服务不可用错误
    
    当API服务不可用或健康检查失败时抛出此异常。
    """
    
    def __init__(self, message: str = "API服务不可用"):
        super().__init__(message)


class ExecutionError(BrightnessClientError):
    """
    执行错误
    
    当队列执行过程中出现错误时抛出此异常。
    包含执行状态和错误详情。
    """
    
    def __init__(self, message: str, execution_status: Optional[str] = None,
                 error_details: Optional[Dict[str, Any]] = None):
        details = {}
        if execution_status:
            details["execution_status"] = execution_status
        if error_details:
            details.update(error_details)
        
        super().__init__(message, details)
        self.execution_status = execution_status


class TimeoutError(BrightnessClientError):
    """
    超时错误
    
    当操作超时时抛出此异常。
    """
    
    def __init__(self, message: str = "操作超时", timeout_seconds: Optional[int] = None):
        details = {"timeout_seconds": timeout_seconds} if timeout_seconds else {}
        super().__init__(message, details)


class ValidationError(BrightnessClientError):
    """
    参数验证错误
    
    当传入的参数不符合要求时抛出此异常。
    """
    
    def __init__(self, message: str, parameter: Optional[str] = None):
        details = {"parameter": parameter} if parameter else {}
        super().__init__(message, details)
