"""
心跳管理器
负责维护API服务的心跳状态，频率为5秒
"""

import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional


class HeartbeatManager:
    """心跳管理器，维护系统状态和连接监控"""
    
    def __init__(self, interval: int = 5):
        """
        初始化心跳管理器
        
        Args:
            interval: 心跳间隔时间（秒），默认5秒
        """
        self.interval = interval
        self.is_running = False
        self.heartbeat_thread = None
        self.start_time = datetime.now()
        self.last_heartbeat = None
        self.system_status = "initializing"
        self.queue_adapter = None
        self._lock = threading.Lock()
        
        # 心跳状态数据
        self._heartbeat_data = {
            "status": "alive",
            "timestamp": None,
            "uptime": 0,
            "queue_status": "idle",
            "device_connected": False,
            "api_requests_count": 0
        }
    
    def set_queue_adapter(self, queue_adapter):
        """设置队列适配器引用"""
        self.queue_adapter = queue_adapter
    
    def start(self):
        """启动心跳管理器"""
        if self.is_running:
            return
        
        self.is_running = True
        self.system_status = "running"
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        print("心跳管理器已启动，间隔: {}秒".format(self.interval))
    
    def stop(self):
        """停止心跳管理器"""
        self.is_running = False
        self.system_status = "stopped"
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=1)
        print("心跳管理器已停止")
    
    def _heartbeat_loop(self):
        """心跳循环线程"""
        while self.is_running:
            try:
                self._update_heartbeat_data()
                time.sleep(self.interval)
            except Exception as e:
                print(f"心跳更新错误: {e}")
                time.sleep(self.interval)
    
    def _update_heartbeat_data(self):
        """更新心跳数据"""
        with self._lock:
            current_time = datetime.now()
            self.last_heartbeat = current_time
            
            # 计算运行时间
            uptime_seconds = int((current_time - self.start_time).total_seconds())
            
            # 获取队列状态
            queue_status = "idle"
            device_connected = False
            execution_status = "idle"
            has_errors = False

            if self.queue_adapter:
                try:
                    # 快速获取基本队列状态
                    queue_info = self.queue_adapter.get_queue_status()
                    queue_status = "running" if queue_info.get("is_running", False) else "idle"
                    device_connected = self.queue_adapter.is_device_connected()

                    # 尝试获取详细执行状态，但设置超时保护
                    try:
                        exec_status = self.queue_adapter.get_execution_status()
                        if "error" not in exec_status:
                            execution_status = exec_status.get("execution_summary", {}).get("execution_status", "idle")
                            error_count = exec_status.get("execution_summary", {}).get("error_count", 0)
                            has_errors = error_count > 0
                        else:
                            execution_status = "error"
                            has_errors = True
                    except Exception:
                        # 如果获取详细状态失败，使用基本状态
                        execution_status = queue_status
                        has_errors = False

                except Exception:
                    queue_status = "error"
                    execution_status = "error"
                    device_connected = False
                    has_errors = True
            
            # 更新心跳数据
            self._heartbeat_data.update({
                "status": "alive",
                "timestamp": current_time.isoformat(),
                "uptime": uptime_seconds,
                "queue_status": queue_status,
                "execution_status": execution_status,
                "device_connected": device_connected,
                "has_errors": has_errors
            })
    
    def get_heartbeat_data(self) -> Dict[str, Any]:
        """获取当前心跳数据"""
        with self._lock:
            return self._heartbeat_data.copy()
    
    def increment_api_requests(self):
        """增加API请求计数"""
        with self._lock:
            self._heartbeat_data["api_requests_count"] += 1
    
    def is_healthy(self) -> bool:
        """检查系统是否健康"""
        if not self.is_running:
            return False
        
        # 检查心跳是否超时（超过间隔时间的3倍认为异常）
        if self.last_heartbeat:
            time_since_last = (datetime.now() - self.last_heartbeat).total_seconds()
            return time_since_last < (self.interval * 3)
        
        return True
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        heartbeat_data = self.get_heartbeat_data()
        
        return {
            "system_status": self.system_status,
            "heartbeat_interval": self.interval,
            "is_healthy": self.is_healthy(),
            "start_time": self.start_time.isoformat(),
            "heartbeat": heartbeat_data
        }
    
    def format_uptime(self, uptime_seconds: int) -> str:
        """格式化运行时间"""
        hours = uptime_seconds // 3600
        minutes = (uptime_seconds % 3600) // 60
        seconds = uptime_seconds % 60
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"
