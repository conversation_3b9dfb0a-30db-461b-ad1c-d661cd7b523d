"""
API专用日志系统
支持按日期分割和自动清理
"""

import logging
import os
import glob
from datetime import datetime, timedelta


class APILogger:
    """API专用日志管理器"""

    def __init__(self, log_dir: str = 'logs', retention_days: int = 30):
        """
        初始化API日志系统

        Args:
            log_dir: 日志目录路径
            retention_days: 日志保留天数
        """
        self.log_dir = log_dir
        self.retention_days = retention_days
        self.logger = None
        self._setup_logger()

    def _get_daily_api_log_filename(self) -> str:
        """
        生成当天的API日志文件名

        Returns:
            完整的API日志文件路径，格式为: logs/api_2025-07-06.log
        """
        today = datetime.now().strftime('%Y-%m-%d')
        filename = f"api_{today}.log"
        return os.path.join(self.log_dir, filename)
    
    def _setup_logger(self):
        """设置API日志记录器"""
        # 确保日志目录存在
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir, exist_ok=True)

        # 创建API专用logger
        self.logger = logging.getLogger('API')
        self.logger.setLevel(logging.INFO)

        # 避免重复添加handler
        if not self.logger.handlers:
            # API日志文件处理器 - 使用当天日期的文件名
            api_log_file = self._get_daily_api_log_filename()
            file_handler = logging.FileHandler(
                filename=api_log_file,
                encoding='utf-8',
                mode='a'  # 追加模式，如果文件存在则追加
            )

            # 设置日志格式
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
            )
            file_handler.setFormatter(formatter)

            self.logger.addHandler(file_handler)

        # 执行启动时的日志清理
        self._cleanup_old_logs()
    
    def _cleanup_old_logs(self):
        """清理过期的API日志文件"""
        if not os.path.exists(self.log_dir):
            return

        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        deleted_count = 0

        # 查找API日志文件（新旧格式）
        log_patterns = [
            os.path.join(self.log_dir, 'api.log.*'),  # 旧格式: api.log.2025-07-04
            os.path.join(self.log_dir, 'api_????-??-??.log'),  # 新格式: api_2025-07-06.log
        ]

        for pattern in log_patterns:
            for log_file in glob.glob(pattern):
                try:
                    filename = os.path.basename(log_file)
                    file_date = None

                    # 尝试从文件名中提取日期（新格式）
                    if filename.startswith('api_') and filename.endswith('.log'):
                        try:
                            date_part = filename.replace('api_', '').replace('.log', '')
                            file_date = datetime.strptime(date_part, '%Y-%m-%d')
                        except ValueError:
                            pass

                    # 如果无法从文件名提取日期，使用文件修改时间（旧格式）
                    if file_date is None:
                        file_mtime = os.path.getmtime(log_file)
                        file_date = datetime.fromtimestamp(file_mtime)

                    # 如果文件超过保留天数，则删除
                    if file_date < cutoff_date:
                        os.remove(log_file)
                        deleted_count += 1
                        print(f"已删除过期API日志文件: {filename}")

                except Exception as e:
                    print(f"删除API日志文件失败 {log_file}: {e}")

        if deleted_count > 0:
            self.logger.info(f"API日志清理完成，共删除 {deleted_count} 个过期文件")
    
    def info(self, message: str):
        """记录信息级别日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告级别日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误级别日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试级别日志"""
        self.logger.debug(message)
    
    def log_request(self, method: str, endpoint: str, params: dict = None, response_status: int = 200):
        """记录API请求日志"""
        params_str = f" - 参数: {params}" if params else ""
        self.logger.info(f"API请求: {method} {endpoint}{params_str} - 状态: {response_status}")
    
    def log_error(self, endpoint: str, error: Exception):
        """记录API错误日志"""
        self.logger.error(f"API错误 {endpoint}: {str(error)}")
    
    def get_logger(self):
        """获取原始logger对象"""
        return self.logger
