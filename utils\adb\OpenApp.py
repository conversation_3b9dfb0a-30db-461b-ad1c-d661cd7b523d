import time
import logging

from utils.adb.BaseAdb import BaseAdb
from utils.adb.ExAdb import ExAdb
from utils.ocr import OpenView as ov
from utils.AppConfig import AppConfig
from enums.PackageName import PackageName


class OpenApp(BaseAdb):
    def __init__(self):
        BaseAdb.__init__(self)
        self.ex_adb = ExAdb()
        self.base_adb = BaseAdb()
        self.device_id = None
        self.app_config = None
        # 兼容旧版本，保留package_dict和package_key
        self.package_dict = {item.value.get("desc"): item.value.get("package") for item in PackageName}
        self.package_key = {item.value.get("desc"): item.name for item in PackageName}
        # 添加日志对象
        self.logs = logging.getLogger("OpenApp")

    def set_device_id(self, device_id):
        self.device_id = device_id
        
    def set_app_config(self, app_config):
        """设置应用配置"""
        self.app_config = app_config

    def get_package_name(self, desc):
        """
        根据描述获取包名
        
        Args:
            desc (str): 应用描述或包名
            
        Returns:
            str: 应用包名，如果找不到则返回默认包名(com.android.settings)
        """
        # 对None值进行处理
        if desc is None:
            self.logs.error(f"应用描述为None")
            return "com.android.settings"  # 默认返回设置应用
        
        # 如果输入本身就是包名（包含点号），直接返回
        if "." in desc:
            return desc
        
        # 优先使用AppConfig获取包名（如果已设置AppConfig）
        if self.app_config:
            package = self.app_config.get_app_package(desc)
            if package:
                return package
        
        # 兼容旧版本，使用package_dict
        package = self.package_dict.get(desc)
        if package:
            return package
        
        # 如果找不到包名，记录错误并返回默认包名
        self.logs.error(f"找不到应用 '{desc}' 的包名")
        return "com.android.settings"  # 默认返回设置应用

    def open_apps(self, strs):
        self.base_adb.open_app(package_name=strs, dev_id=self.device_id)

    def start_activity(self, package, activity, device_id):
        self.base_adb.open_app_activity(package_name=package, activity_name=activity)

    def open_app(self, package_name_or_desc, device_id=None):
        """
        根据包名或描述打开应用
        :param package_name_or_desc: 包名或应用描述
        :param device_id: 设备ID
        :return: 应用描述
        """
        if not self.app_config:
            # 如果没有设置应用配置，先尝试获取包名再打开
            package_name = self.get_package_name(package_name_or_desc)
            self.open_apps(package_name)
            return package_name_or_desc
            
        # 尝试获取应用配置
        app_info = self.app_config.get_app_by_desc(package_name_or_desc)
        
        if not app_info:
            # 如果没有找到应用配置，使用get_package_name方法获取包名
            package_name = self.get_package_name(package_name_or_desc)
            self.open_apps(package_name)
            return package_name_or_desc
            
        # 根据打开方法选择打开策略
        open_method = app_info.get("open_method", "package")
        
        if open_method == "package":
            # 使用包名打开
            package = app_info.get("package", "")
            if not package:
                # 如果配置中没有包名，使用get_package_name方法获取
                package = self.get_package_name(package_name_or_desc)
            self.open_apps(package)
                
        elif open_method == "activity":
            # 使用Activity打开
            package = app_info.get("package", "")
            activity = app_info.get("activity", "")
            if not package:
                # 如果配置中没有包名，使用get_package_name方法获取
                package = self.get_package_name(package_name_or_desc)
            if package and activity:
                self.start_activity(package, activity, device_id or self.device_id)
            else:
                self.open_apps(package)
                
        elif open_method == "ui":
            # 使用UI查找打开
            app_name = app_info.get("app_name", package_name_or_desc)
            try:
                import uiautomator2 as u2
                d = u2.connect(device_id or self.device_id)

                # 按两次Home键返回桌面
                self.on_home(device_id=d.serial)
                time.sleep(2)
                self.on_home(device_id=d.serial)
                time.sleep(2)

                found = False
                # 循环查找并滑动，最多20次
                for i in range(20):
                    if d(text=app_name).exists:
                        d(text=app_name).click()
                        found = True
                        self.logs.info(f"通过UI在第 {i + 1}屏 找到并打开了应用 '{app_name}'")
                        break
                    
                    # 如果未找到，向左滑动
                    self.ex_adb.page_left_to(device_id=d.serial)
                    time.sleep(2)  # 等待滑动动画

                if not found:
                    self.logs.warning(f"UI打开应用失败: 滑动20次后仍未找到 '{app_name}'")
                    # 根据用户要求，找不到应用时不执行任何操作，仅提示，并让主流程继续

            except Exception as e:
                self.logs.error(f"UI打开应用时发生异常: {e}")
                # 异常时的回退逻辑
                package = app_info.get("package", "")
                activity = app_info.get("activity", "")

                # 如果没有包名，尝试从别名获取
                if not package:
                    package = self.get_package_name(package_name_or_desc)

                # 优先尝试Activity打开
                if package and activity:
                    self.logs.info(f"UI打开失败，回退到Activity方式打开: {package}/{activity}")
                    self.start_activity(package, activity, device_id or self.device_id)
                # 其次尝试包名打开
                elif package:
                    self.logs.info(f"UI打开失败，回退到Package方式打开: {package}")
                    self.open_apps(package)
                # 都失败了
                else:
                    self.logs.error(f"UI打开失败，且无法通过Activity或Package回退: '{package_name_or_desc}'")
        
        return app_info.get("desc", package_name_or_desc)

    def open_desktop(self):
        """ 打开桌面 """
        self.on_home(device_id=self.device_id)
        return "桌面"

    def open_photo(self):
        """ 打开系统相册 """
        self.open_app("相册")
        return "相册"

    def open_negative_one_screen(self):
        """ 打开负一屏 """
        # self.open_apps("com.vivo.hiboard")
        self.on_home(device_id=self.device_id)
        time.sleep(1)
        self.on_home(device_id=self.device_id)
        time.sleep(1)
        self.on_home(device_id=self.device_id)
        time.sleep(1)
        self.on_home(device_id=self.device_id)
        time.sleep(1)
        self.ex_adb.page_right_to(device_id=self.device_id)
        time.sleep(2)
        return "负一屏"

    def open_phone(self):
        """ 打开电话 """
        self.open_app("电话")
        return "电话"

    def open_ex_phone(self):
        """ 打开外销电话 """
        self.open_app("外销电话")
        return "外销电话"

    def open_sms(self):
        """ 打开短信 """
        self.open_app("短信")
        return "短信"

    def open_ex_sms(self):
        """ 打开外销短信 """
        self.open_app("外销短信")
        return "外销短信"

    def open_weixin_video(self):
        """ 打开微信视频通话 """
        self.open_app("微信")
        time.sleep(2)
        ov.open_weixin_video(self.device_id)
        return "微信视频"

    def open_appslication_store(self):
        """ 打开应用商店 """
        self.open_app("应用商店")
        return "应用商店"

    def open_vivo_store(self):
        """ 打开vivo应用商店 """
        self.open_app("vivo应用商店")
        return "vivo应用商店"

    def open_douyin(self):
        """ 打开抖音 """
        self.open_app("抖音")
        return "抖音"

    def open_kuaishou(self):
        """ 打开快手 """
        self.open_app("快手")
        return "快手"

    def open_tengxunshipin(self):
        """ 打开腾讯视频 """
        self.open_app("腾讯视频")
        return "腾讯视频"

    def open_aiqiyi(self):
        """ 打开爱奇艺 """
        self.open_app("爱奇艺")
        return "爱奇艺"

    def open_qq(self):
        """ 打开QQ """
        self.open_app("QQ")
        return "QQ"

    def open_weibo(self):
        """ 打开微博 """
        self.open_app("微博")
        return "微博"

    def open_ex_facebook(self):
        """ 打开facebook """
        self.open_app("Facebook")
        return "Facebook"


    def open_kugou(self):
        """ 打开酷狗音乐 """
        self.open_app("酷狗音乐")
        return "酷狗音乐"

    def open_weixin(self):
        """ 打开微信 """
        self.open_app("微信")
        return "微信"

    def open_gaode(self):
        """ 打开高德地图 """
        self.open_app("高德地图")
        return "高德地图"

    def open_yuanshen(self):
        """ 打开原神 """
        self.open_app("原神")
        return "原神"

    def open_xingqiongtiedao(self):
        """ 打开星穹铁道 """
        self.open_app("星穹铁道")
        return "星穹铁道"

    def open_wangzherongyaoo(self):
        """ 打开王者荣耀 """
        self.open_app("王者荣耀")
        return "王者荣耀"

    def open_hepingjingying(self):
        """ 打开和平精英 """
        self.open_app("和平精英")
        return "和平精英"

    def open_ex_pubg(self):
        """ 国际版 pubg 吃鸡 """
        self.open_app("PUBG国际版")
        return "PUBG国际版"

    def open_ex_yuanshen(self):
        """ 国际版原神 """
        self.open_app("国际版原神")
        return "国际版原神"

    def open_jinchanchan(self):
        """ 金铲铲 """
        self.open_app("金铲铲")
        return "金铲铲"

    def open_ex_TFT(self):
        """ 国际版 金铲铲 云顶之弈 """
        self.open_app("国际版金铲铲")
        return "国际版金铲铲"

    def open_ex_xingqiongtiedao(self):
        """ 国际版 星穹铁道 """
        self.open_app("国际版星穹铁道")
        return "国际版星穹铁道"

    def open_lolm(self):
        """ 英雄联盟 """
        self.open_app("英雄联盟")
        return "英雄联盟"

    def open_ex_lolm(self):
        """ 国际版 英雄联盟 """
        self.open_app("国际版英雄联盟")
        return "国际版英雄联盟"

    def open_ex_chuanshuoduijue(self):
        """ 国际版 传说对决 """
        self.open_app("国际版传说对决")
        return "国际版传说对决"

    def open_ex_wangzherongyao(self):
        """ 国际版 王者荣耀 """
        self.open_app("国际版王者荣耀")
        return "国际版王者荣耀"


    def open_tiktok(self):
        """ 抖音国际版 """
        self.open_app("TikTok")
        return "TikTok"

    def open_youtube(self):
        """ YouTube """
        self.open_app("YouTube")
        return "YouTube"

    def open_google_maps(self):
        """ 谷歌地图 """
        self.open_app("Google Maps")
        return "Google Maps"

    def open_ex_application_store(self):
        """ 外销应用商店 """
        self.open_app("外销vivo商店")
        return "外销vivo商店"

    def open_ex_vivo_store(self):
        """ 外销vivo商店 """
        self.open_app("外销vivo商店")
        return "外销vivo商店"



