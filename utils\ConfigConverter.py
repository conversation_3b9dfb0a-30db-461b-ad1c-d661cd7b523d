import json
import os
from enums.PackageName import PackageName
import config


class ConfigConverter:
    """配置转换工具，用于将PackageName枚举转换为JSON配置文件"""
    
    @staticmethod
    def convert_to_json(output_path="app_config.json"):
        """
        将PackageName枚举转换为JSON配置文件
        :param output_path: 输出文件路径
        :return: 是否成功
        """
        try:
            # 获取5.0和4.0配置
            config_5 = config.get_5_config()
            config_4 = config.get_4_config()
            
            app_config = {}
            
            # 处理每个枚举成员
            for item in PackageName:
                key = item.name
                value_5 = config_5.get(key, {})
                value_4 = config_4.get(key, {})
                
                # 基本信息
                app_info = {
                    "desc": value_5.get("desc", ""),
                    "package": value_5.get("package", ""),
                    "activity": "",  # 默认为空，需要手动补充
                    "app_name": value_5.get("desc", ""),  # 默认使用desc作为app_name
                    "open_method": "package",  # 默认使用package方式打开
                    "standards": {
                        "5.0": {
                            "indoor": value_5.get("indoor", "0/0"),
                            "outdoor": value_5.get("outdoor", "0/0")
                        },
                        "4.0": {}
                    }
                }
                
                # 处理4.0配置（结构更复杂）
                if isinstance(value_4.get("indoor", {}), dict):
                    # 4.0配置是按亮度值分级的
                    indoor_4 = {}
                    outdoor_4 = {}
                    
                    # 合并所有亮度值的配置为一个字符串
                    for nit, value in value_4.get("indoor", {}).items():
                        if value != "0/0":
                            indoor_4[nit] = value
                    
                    for nit, value in value_4.get("outdoor", {}).items():
                        if value != "0/0":
                            outdoor_4[nit] = value
                    
                    app_info["standards"]["4.0"] = {
                        "indoor": indoor_4,
                        "outdoor": outdoor_4
                    }
                else:
                    # 简单结构，直接使用
                    app_info["standards"]["4.0"] = {
                        "indoor": value_4.get("indoor", "0/0"),
                        "outdoor": value_4.get("outdoor", "0/0")
                    }
                
                # 添加应用类型
                app_types = []
                
                # 检查应用是否在各个列表中
                if key in [item.name for item in PackageName.get_package_auto_list()]:
                    app_types.append("auto")
                
                if key in [item.name for item in PackageName.get_package_manua_list()]:
                    app_types.append("manual")
                
                if key in [item.name for item in PackageName.get_package_ex_auto_list()]:
                    app_types.append("ex_auto")
                
                if key in [item.name for item in PackageName.get_package_ex_manua_list()]:
                    app_types.append("ex_manual")
                
                if key in [item.name for item in PackageName.get_package_outdoor_auto_and_manua_list()]:
                    app_types.append("outdoor")
                
                app_info["type"] = app_types
                
                # 添加到配置
                app_config[key] = app_info
            
            # 保存到文件
            with open(output_path, 'w', encoding='utf-8') as json_file:
                json.dump(app_config, json_file, ensure_ascii=False, indent=4)
            
            return True
        
        except Exception as e:
            print(f"转换配置失败: {e}")
            return False
    
    @staticmethod
    def validate_config(config_path="app_config.json"):
        """
        验证配置文件是否有效
        :param config_path: 配置文件路径
        :return: 是否有效
        """
        try:
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as json_file:
                app_config = json.load(json_file)
            
            # 检查必要字段
            for key, app_info in app_config.items():
                if not app_info.get("desc"):
                    print(f"应用 {key} 缺少desc字段")
                    return False
                
                if not app_info.get("package"):
                    print(f"应用 {key} 缺少package字段")
                    return False
                
                if not app_info.get("standards"):
                    print(f"应用 {key} 缺少standards字段")
                    return False
                
                if not app_info.get("type"):
                    print(f"应用 {key} 缺少type字段")
                    return False
            
            return True
        
        except Exception as e:
            print(f"验证配置失败: {e}")
            return False 