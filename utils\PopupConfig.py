import json
import os


# 定义默认弹窗监控规则
DEFAULT_POPUP_RULES = {
    "rules": [
        {
            "name": "权限请求弹窗",
            "type": "text",
            "keywords": ["允许", "权限", "访问"],
            "action": "click",
            "target": "允许"
        },
        {
            "name": "更新提示弹窗",
            "type": "text",
            "keywords": ["更新", "新版本"],
            "action": "click",
            "target": "取消"
        },
        {
            "name": "广告弹窗",
            "type": "id",
            "resource_id": "com.android.packageinstaller:id/permission_allow_button",
            "action": "click"
        }
    ],
    "enabled": True,
    "check_interval": 2,  # 检查间隔（秒）
    "max_retries": 3      # 最大重试次数
}


class PopupConfig:
    """弹窗监控配置管理类，用于从JSON文件中读取弹窗监控规则"""
    def __init__(self, config_path="popup_config.json"):
        """
        初始化弹窗监控配置管理类
        :param config_path: 配置文件路径
        """
        self.config_path = config_path
        self.popup_config = self._load_config()
    
    def _load_config(self):
        """加载JSON配置文件"""
        if not os.path.exists(self.config_path):
            # 如果配置文件不存在，尝试在_internal目录下查找
            internal_path = os.path.join("_internal", self.config_path)
            if os.path.exists(internal_path):
                self.config_path = internal_path
            else:
                # 如果配置文件不存在，返回默认配置
                return DEFAULT_POPUP_RULES
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as json_file:
                return json.load(json_file)
        except Exception as e:
            print(f"加载弹窗配置文件失败: {e}")
            return DEFAULT_POPUP_RULES
    
    def get_rules(self):
        """获取所有弹窗规则"""
        return self.popup_config.get("rules", [])
    
    def is_enabled(self):
        """检查弹窗监控是否启用"""
        return self.popup_config.get("enabled", True)
    
    def get_check_interval(self):
        """获取检查间隔时间（秒）"""
        return self.popup_config.get("check_interval", 2)
    
    def get_max_retries(self):
        """获取最大重试次数"""
        return self.popup_config.get("max_retries", 3)
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as json_file:
                json.dump(self.popup_config, json_file, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存弹窗配置文件失败: {e}")
            return False
    
    def update_rule(self, rule_name, new_rule):
        """更新弹窗规则"""
        rules = self.get_rules()
        for i, rule in enumerate(rules):
            if rule.get("name") == rule_name:
                rules[i] = new_rule
                self.popup_config["rules"] = rules
                return self.save_config()
        return False
    
    def add_rule(self, new_rule):
        """添加弹窗规则"""
        rules = self.get_rules()
        # 检查是否已存在同名规则
        for rule in rules:
            if rule.get("name") == new_rule.get("name"):
                return False
        rules.append(new_rule)
        self.popup_config["rules"] = rules
        return self.save_config()
    
    def remove_rule(self, rule_name):
        """删除弹窗规则"""
        rules = self.get_rules()
        for i, rule in enumerate(rules):
            if rule.get("name") == rule_name:
                rules.pop(i)
                self.popup_config["rules"] = rules
                return self.save_config()
        return False
    
    def set_enabled(self, enabled):
        """设置是否启用弹窗监控"""
        self.popup_config["enabled"] = enabled
        return self.save_config()
    
    def set_check_interval(self, interval):
        """设置检查间隔时间"""
        self.popup_config["check_interval"] = interval
        return self.save_config()
    
    def set_max_retries(self, max_retries):
        """设置最大重试次数"""
        self.popup_config["max_retries"] = max_retries
        return self.save_config()
    
    def restore_default(self):
        """恢复默认配置"""
        self.popup_config = DEFAULT_POPUP_RULES.copy()
        return self.save_config()
    
    def reload(self):
        """重新加载配置文件"""
        self.popup_config = self._load_config()
        return True 