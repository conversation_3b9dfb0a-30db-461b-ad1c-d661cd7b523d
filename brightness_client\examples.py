"""
亮度测试客户端使用示例

展示如何在第三方应用程序中集成和使用brightness_client库。
"""

import time
import logging
from brightness_client import BrightnessTestClient, BrightnessClientError


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建客户端
    client = BrightnessTestClient()
    
    try:
        # 检查服务状态
        if not client.is_service_available():
            print("API服务不可用，请确保主程序已启动")
            return
        
        print("API服务可用")
        
        # 获取服务信息
        info = client.get_service_info()
        print(f"设备数量: {info['devices']['count']}")
        print(f"可用预设: {list(info['presets'].keys())}")
        
        # 清空现有队列
        client.clear_queue()
        
        # 添加内销OLED自动测试
        result = client.add_内_OLED_自动()
        print("已添加内销OLED自动测试队列")
        
        # 查看队列状态
        queue = client.get_queue()
        print(f"队列数量: {queue.queue_count}")
        
        # 启动执行
        client.start_execution()
        print("已启动执行")
        
        # 等待完成（最多等待10分钟）
        final_status = client.wait_for_completion(timeout=600)
        print(f"执行完成，状态: {final_status.summary.execution_status}")
        
    except BrightnessClientError as e:
        print(f"客户端错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}")
    finally:
        client.close()


def example_preset_methods():
    """预设方法使用示例"""
    print("\n=== 预设方法使用示例 ===")
    
    with BrightnessTestClient() as client:
        try:
            if not client.is_service_available():
                print("API服务不可用")
                return
            
            # 清空队列
            client.clear_queue()
            
            # 5.0版本预设示例
            print("添加5.0版本预设...")
            client.add_内_OLED_自动()
            client.add_内_OLED_手动()
            client.add_外_OLED_自动()
            
            # 4.0版本预设示例
            print("添加4.0版本预设...")
            brightness_values = [30, 100, 300]
            client.add_内销自动(brightness_values)
            client.add_内销手动(brightness_values)
            
            # 查看队列
            queue = client.get_queue()
            print(f"总共添加了 {queue.queue_count} 个队列")
            
            for i, queue_info in enumerate(queue.queue_list):
                print(f"  {i+1}. {queue_info.title}")
            
        except Exception as e:
            print(f"错误: {e}")


def example_batch_operations():
    """批量操作示例"""
    print("\n=== 批量操作示例 ===")
    
    with BrightnessTestClient() as client:
        try:
            if not client.is_service_available():
                print("API服务不可用")
                return
            
            client.clear_queue()
            
            # 批量添加5.0版本预设
            presets_5_0 = ["内_OLED_自动", "内_OLED_手动", "外_OLED_自动"]
            results = client.batch_add_presets_5_0(presets_5_0)
            
            print("5.0版本批量添加结果:")
            for result in results:
                status = "成功" if result["success"] else "失败"
                print(f"  {result['preset']}: {status}")
            
            # 批量添加4.0版本预设
            presets_4_0 = [
                {"preset": "内销自动", "brightness_values": [30, 100, 300]},
                {"preset": "外销自动", "brightness_values": [50, 150, 250]}
            ]
            results = client.batch_add_presets_4_0(presets_4_0)
            
            print("4.0版本批量添加结果:")
            for result in results:
                status = "成功" if result["success"] else "失败"
                print(f"  {result['preset']}: {status}")
            
        except Exception as e:
            print(f"错误: {e}")


def example_monitoring():
    """任务监控示例"""
    print("\n=== 任务监控示例 ===")
    
    def status_callback(status):
        """状态更新回调函数"""
        summary = status.summary
        print(f"执行状态: {summary.execution_status}, "
              f"进度: {summary.completed_queues}/{summary.total_queues}, "
              f"运行时间: {summary.duration_seconds}秒")
    
    with BrightnessTestClient() as client:
        try:
            if not client.is_service_available():
                print("API服务不可用")
                return
            
            client.clear_queue()
            
            # 添加一个快速测试队列
            client.add_内_OLED_自动()
            
            # 启动执行
            client.start_execution()
            
            # 监控执行（带回调）
            print("开始监控执行状态...")
            final_status = client.monitor_execution(callback=status_callback, poll_interval=2)
            
            print(f"监控结束，最终状态: {final_status.summary.execution_status}")
            
            # 检查是否有错误
            if final_status.summary.error_count > 0:
                errors = client.get_execution_errors()
                print(f"执行过程中出现 {errors['error_count']} 个错误")
                if errors.get('last_error'):
                    last_error = errors['last_error']
                    print(f"最后一个错误: {last_error['error_message']}")
            
        except Exception as e:
            print(f"错误: {e}")


def example_custom_queues():
    """自定义队列示例"""
    print("\n=== 自定义队列示例 ===")
    
    with BrightnessTestClient() as client:
        try:
            if not client.is_service_available():
                print("API服务不可用")
                return
            
            client.clear_queue()
            
            # 自定义5.0版本队列
            custom_apps = ["微信", "抖音", "王者荣耀"]
            client.add_custom_queue_5_0(
                apps=custom_apps,
                auto_brightness=True,
                preset_name="自定义游戏测试"
            )
            
            # 自定义4.0版本队列
            client.add_custom_queue_4_0(
                apps=["微信", "QQ"],
                brightness_values=[50, 150, 250],
                auto_brightness=False,
                preset_name="自定义社交应用测试"
            )
            
            queue = client.get_queue()
            print(f"添加了 {queue.queue_count} 个自定义队列")
            
        except Exception as e:
            print(f"错误: {e}")


def example_quick_test():
    """快速测试示例"""
    print("\n=== 快速测试示例 ===")
    
    with BrightnessTestClient() as client:
        try:
            if not client.is_service_available():
                print("API服务不可用")
                return
            
            # 快速内销OLED测试（包含自动和手动）
            print("开始快速内销OLED测试...")
            result = client.quick_test_内_OLED(include_manual=True)
            
            print("测试完成!")
            print(f"测试类型: {result['test_type']}")
            print(f"包含手动测试: {result['include_manual']}")
            print(f"执行状态: {result['execution_status']}")
            print(f"完成队列: {result['completed_queues']}/{result['total_queues']}")
            print(f"耗时: {result['duration_seconds']}秒")
            
        except Exception as e:
            print(f"错误: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 使用错误的URL测试连接错误
    client = BrightnessTestClient(base_url="http://127.0.0.1:9999")
    
    try:
        client.health_check()
    except BrightnessClientError as e:
        print(f"捕获到客户端错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        if hasattr(e, 'details'):
            print(f"错误详情: {e.details}")
    finally:
        client.close()


if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    print("亮度测试客户端使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_basic_usage()
    example_preset_methods()
    example_batch_operations()
    example_monitoring()
    example_custom_queues()
    example_quick_test()
    example_error_handling()
    
    print("\n所有示例运行完成!")
