import os
import time
from utils.Util import Util
from component.GlobalInstanceBase import GlobalInstanceBase
from qgui import MessageBox
from qgui.notebook_tools import RadioButton
from enums.PackageName import PackageName
from queues.TestFourQueue import TestFourQueue
from utils.adb.ExAdb import ExAdb
from utils.PresetsConfig import PresetsConfig


class FourController:
    """ 亮度降幅4.0控制器 """
    def __init__(self, guiModel, logs):
        self.guiModel = guiModel
        self.global_instance = None
        self.queue_list = {}
        self.test_queue = TestFourQueue()
        self.is_auto_brightness = False
        self.adb = ExAdb()
        self.logs = logs
        self.util = Util()
        self.nit_arr_int = None
        # 初始化预设配置
        self.presets_config = PresetsConfig(version="4.0")
        self.test_queue.start_brightness_thread()
        # 设置日志实例
        self.test_queue.set_logs(self.logs)

    def set_global_instance(self, global_instance: GlobalInstanceBase):
        self.global_instance = global_instance

    def click(self, args: dict):
        args["进度条Four"].set(0)
        
        # 判断是否连接设备
        if not self.is_device_connected():
            MessageBox.info("未连接设备，请点击顶部的连接设备按钮")
            return
        
        # 确保测试队列的亮度线程已启动
        if not self.test_queue.get_is_start_brightness_thread():
            self.start_brightness_thread()
        
        # 判断是否设置了测试亮度
        if self.nit_arr_int is None or len(self.nit_arr_int) == 0:
            MessageBox.info("请先设置测试亮度！")
            return
        
        # 重置文件
        self.test_queue.set_file_name(None)
        self.test_queue.set_nit_arr_int(self.nit_arr_int)
        self.test_queue.set_progress_bar(args["进度条Four"])
        
        # 获取手动选择的队列
        queue_list = self.global_instance.get_instance("QueueDisplayFour").get_all_task_names()
        self.test_queue.set_queue_data(queue_list)
        self.test_queue.set_is_auto_brightness(self.is_auto_brightness)
        
        # 设置文件信息和测试名称
        sheet_name = "自动" if self.is_auto_brightness else "手动"

        radio_selection = args['RadioObviousToolFour'].get()
        if "外销" in radio_selection:
            sheet_name = "外销" + sheet_name
            
        self.test_queue.set_file_info(sheet_name)
        
        # 执行测试
        self.test_queue.start_test_threads(sheet_name=sheet_name, add_separator=True, is_headers=False)
        
        # 添加元数据
        device_list = self.adb.get_device_list()
        if len(device_list) > 0:
            from utils.ExcelSave import ExcelSave
            for device_id in device_list:
                excel_model = ExcelSave(device_id=device_id, filename=self.test_queue.get_file_name(), sheet_name=sheet_name, append_mode=True)
                excel_model.add_metadata_to_sheet(sheet_name=sheet_name)
                # 保存文件后立即关闭，避免持续占用
                excel_model.save(self.test_queue.get_file_name())
                break
        
        # 显示测试完成提示
        MessageBox.info("测试完成")
        
        # 在控制台输出详细信息
        self.logs.info(f"测试完成，文件保存为：{self.test_queue.get_file_name()}")

    def add_queue_package(self, args: dict):
        nit_str = args['InputPackageNameFour'].get()
        if "示例" in nit_str:
            MessageBox.info("请删除示例输入正确的亮度值!")
            return
        if not nit_str:
            MessageBox.info("请输入亮度值!")
            return
        if "，" in nit_str:
            MessageBox.info("请使用英文状态下的逗号!")
            return
        # 判断nit_str最后一个字符是否为数字
        if not nit_str[-1].isdigit():
            MessageBox.info("非法格式!")
            return
        try:
            nit_arr = nit_str.split(',')
            self.nit_arr_int = [int(value) for value in nit_arr]
            MessageBox.info("设置成功")
        except ValueError:
            MessageBox.info("非法格式!")
            return

        # 新增队列
        # self.global_instance.get_instance("QueueDisplayFour").add_task(new_name)
        # self.queue_list[new_name] = new_name

    def add_queue_sub(self, item: tuple):
        name, flag = item
        if flag:
            # 新增队列
            self.global_instance.get_instance("QueueDisplayFour").add_task(name)
        else:
            # 删除队列
            self.global_instance.get_instance("QueueDisplayFour").remove_task(name)

    def queue_click(self, name: str):
        # 更改多选按钮状态
        self.global_instance.get_instance("CheckObviousToolLineFour").update_box(name, False)

    def set_is_auto_brightness(self, is_auto_brightness):
        self.is_auto_brightness = is_auto_brightness
        self.global_instance.get_instance("AutoBrightnessSwitchFour").set_status(self.is_auto_brightness, "自动亮度")
        if self.is_auto_brightness:
            self.adb.start_auto_brightness()
        else:
            self.adb.close_auto_brightness()

    def auto_brightness_switch(self, args: dict):
        if args['AutoBrightnessSwitchFour-自动亮度'].get() == "1":
            self.set_is_auto_brightness(True)
        else:
            self.set_is_auto_brightness(False)

    def preinstall_click(self, args: dict):
        """ 预设单选按钮 """
        radio_selection = args['RadioObviousToolFour'].get()

        # 清空队列
        self.global_instance.get_instance("QueueDisplayFour").clear_all_tasks()
        self.global_instance.get_instance("CheckObviousToolLineFour").update_box_all(False)

        preset_key = None
        fallback_packages = []
        
        if radio_selection == "自动":
            preset_key = "auto"
            fallback_packages = PackageName.get_package_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "手动":
            preset_key = "manual"
            fallback_packages = PackageName.get_package_auto_and_manual_list_4_0()
            self.set_is_auto_brightness(False)
        elif radio_selection == "外销自动":
            preset_key = "ex_auto"
            fallback_packages = PackageName.get_package_ex_auto_list()
            self.set_is_auto_brightness(True)
        elif radio_selection == "外销手动":
            preset_key = "ex_manual"
            fallback_packages = PackageName.get_package_ex_manual_list()
            self.set_is_auto_brightness(False)
        elif radio_selection == "清空队列":
            return  # Do nothing else

        if preset_key:
            app_list = self.presets_config.get_preset_apps(preset_key)
            if app_list:
                for app_name in app_list:
                    self.global_instance.get_instance("QueueDisplayFour").add_task(app_name)
            elif fallback_packages:
                # Fallback to hardcoded lists
                for item in fallback_packages:
                    self.global_instance.get_instance("QueueDisplayFour").add_task(item.value.get("desc"))

    def testing_standard_click(self, args: dict):
        """ 测试标准切换 """
        if args['TestingStandardTool'].get() == "室内标准":
            self.test_queue.set_test_standard("indoor")
        elif args['TestingStandardTool'].get() == "室外标准":
            self.test_queue.set_test_standard("outdoor")
            
    def save_preset(self, preset_key):
        """保存当前队列为预设"""
        queue_list = self.global_instance.get_instance("QueueDisplayFour").get_all_task_names()
        success = self.presets_config.save_preset_apps(preset_key, queue_list)
        if success:
            MessageBox.info(f"保存预设 {self.presets_config.get_preset_name(preset_key)} 成功")
        else:
            MessageBox.info(f"保存预设 {preset_key} 失败")

    def reload_presets(self):
        """重新加载预设配置"""
        result = self.presets_config.reload()
        
        # 获取当前选中的预设
        try:
            current_preset = self.guiModel.get_data_by_name("RadioObviousToolFour")
            
            # 如果当前有选中预设，则重新加载
            if current_preset and current_preset != "清空队列":
                # 清空队列
                self.global_instance.get_instance("QueueDisplayFour").clear_all_tasks()
                self.global_instance.get_instance("CheckObviousToolLineFour").update_box_all(False)
                
                preset_key = None
                if current_preset == "自动":
                    preset_key = "auto"
                elif current_preset == "手动":
                    preset_key = "manual"
                elif current_preset == "外销自动":
                    preset_key = "ex_auto"
                elif current_preset == "外销手动":
                    preset_key = "ex_manual"
                
                if preset_key:
                    # 获取最新的预设应用列表
                    app_list = self.presets_config.get_preset_apps(preset_key)
                    if app_list:
                        for app_name in app_list:
                            # 新增队列
                            self.global_instance.get_instance("QueueDisplayFour").add_task(app_name)
        except Exception as e:
            print(f"刷新预设失败: {e}")
            
        return result

    def connect_device(self, args: dict):
        """ 连接设备 """
        # 获取设备列表
        device_list = self.adb.get_device_list()
        if not device_list or len(device_list) == 0:
            info_str = "未检测到设备，请检查设备连接"
            self.logs.warning(info_str)
            
            # 如果不是静默模式，显示消息
            if not args.get("silent", False):
                MessageBox.info(info_str)
            return False
        
        # 执行root操作
        try:
            self.logs.info("正在尝试获取root权限...")
            self.adb.get_root()
            self.logs.info("root操作完成")
        except Exception as e:
            self.logs.error(f"获取root权限失败: {e}")
        
        # 安装ATX.apk
        try:
            self.logs.info("正在安装ATX.apk...")
            if self.adb.install_atx_app():
                self.logs.info("ATX.apk安装成功")
            else:
                self.logs.warning("ATX.apk安装失败，可能影响部分功能")
        except Exception as e:
            self.logs.error(f"安装ATX.apk失败: {e}")
        
        # 更新设备连接信息
        info_str = self.util.connect_info_str(device_list)
        self.logs.info("\n连接设备："+info_str)
        
        # 启动测试队列
        self.test_queue.start_brightness_thread()
        
        return True

    def set_adb(self, adb):
        """
        设置ADB实例，以便共享设备连接状态
        :param adb: ADB实例
        """
        self.adb = adb
        # 更新测试队列的ADB实例，但保持测试队列对象独立
        if hasattr(self, 'test_queue') and self.test_queue:
            self.test_queue.adb = adb
        
    def start_brightness_thread(self):
        """
        启动亮度线程，与5.0面板共享设备连接状态
        """
        if not self.test_queue.get_is_start_brightness_thread():
            self.test_queue.start_brightness_thread()

    def set_main_controller(self, main_controller):
        """
        设置主控制器，以便检查设备连接状态
        :param main_controller: 主控制器实例
        """
        self.main_controller = main_controller
        
    def is_device_connected(self):
        """
        检查设备是否已连接（优先使用主控制器的状态）
        :return: 设备连接状态
        """
        # 如果主控制器已设置，使用主控制器的状态
        if hasattr(self, 'main_controller') and self.main_controller:
            # 直接访问主控制器的is_device_connected属性，而不是调用方法
            return getattr(self.main_controller, 'is_device_connected', False)
        
        # 否则检查测试队列状态
        return self.test_queue.get_is_start_brightness_thread()

    def add_to_execution_queue(self, args: dict = None):
        """
        将当前队列添加到执行队列
        
        Args:
            args: UI参数字典，可选
        """
        # 确认有队列控制器可用
        queue_controller = self.global_instance.get_instance("QueueController")
        if queue_controller is None:
            self.logs.error("队列控制器未初始化")
            MessageBox.info("队列控制器未初始化")
            return False
        
        # 确认已设置测试亮度
        if self.nit_arr_int is None or len(self.nit_arr_int) == 0:
            self.logs.warning("未设置测试亮度，无法添加到执行队列")
            MessageBox.info("请先设置测试亮度！")
            return False
        
        # 获取当前队列设置
        queue_list = self.global_instance.get_instance("QueueDisplayFour").get_all_task_names()
        
        # 如果队列为空，提示用户
        if not queue_list or len(queue_list) == 0:
            self.logs.warning("当前队列为空，无法添加到执行队列")
            MessageBox.info("当前队列为空，请选择应用后再添加")
            return False
        
        # 创建一个TestFourQueue实例
        test_queue = TestFourQueue()
        test_queue.set_logs(self.logs)
        test_queue.set_queue_data(queue_list)
        test_queue.set_is_auto_brightness(self.is_auto_brightness)
        test_queue.set_nit_arr_int(self.nit_arr_int)
        
        # 设置队列标题
        radio_selection = None
        if args and 'RadioObviousToolFour' in args:
            radio_selection = args['RadioObviousToolFour'].get()
        
        # 根据当前选项生成队列标题
        if radio_selection:
            title = f"4.0 - {radio_selection}"
        else:
            title = f"4.0 - {'自动亮度' if self.is_auto_brightness else '手动亮度'}"
        
        test_queue.set_queue_title(title)
        
        # 添加到执行队列
        success = queue_controller.add_to_execution_queue(test_queue)
        
        if success:
            self.logs.info(f"已将队列 '{title}' 添加到执行队列")
            MessageBox.info(f"已将队列 '{title}' 添加到执行队列")
            return True
        else:
            MessageBox.info("添加队列失败")
            return False
