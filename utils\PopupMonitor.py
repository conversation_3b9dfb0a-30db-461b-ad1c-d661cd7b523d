import time
import logging
import threading
import os
import sys

# 尝试多种方式导入uiautomator2
try:
    import uiautomator2 as u2
except ImportError as e:
    logging.warning(f"常规方式导入uiautomator2失败: {e}")
    try:
        import importlib.util
        import sys
        
        # 尝试从site-packages直接导入
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
            # 尝试多个可能的路径
            possible_paths = [
                os.path.join(base_dir, "uiautomator2", "__init__.py"),
                os.path.join(base_dir, "_internal", "uiautomator2", "__init__.py")
            ]
            
            module_spec = None
            for path in possible_paths:
                if os.path.exists(path):
                    logging.info(f"尝试从 {path} 导入uiautomator2")
                    module_spec = importlib.util.spec_from_file_location("uiautomator2", path)
                    break
            
            if module_spec:
                u2 = importlib.util.module_from_spec(module_spec)
                sys.modules["uiautomator2"] = u2
                module_spec.loader.exec_module(u2)
                logging.info("成功从备用路径导入uiautomator2")
            else:
                logging.error("找不到uiautomator2模块")
                u2 = None
        else:
            # 开发环境下再次尝试常规导入
            import uiautomator2 as u2
    except Exception as backup_error:
        logging.error(f"备用方式导入uiautomator2也失败: {backup_error}")
        u2 = None

from utils.PopupConfig import PopupConfig


# 添加资源路径处理工具函数
def get_resource_path(package, resource_path):
    """
    获取资源文件路径，兼容打包环境
    :param package: 包名，如'uiautomator2'
    :param resource_path: 资源路径，如'assets/u2.jar'
    :return: 完整资源路径
    """
    # 是否为打包环境
    if getattr(sys, 'frozen', False):
        # 打包环境，使用应用根目录或_internal目录
        base_dir = os.path.dirname(sys.executable)
        
        # 尝试多个可能的路径
        possible_paths = [
            os.path.join(base_dir, package, resource_path),  # 基本路径
            os.path.join(base_dir, "_internal", package, resource_path),  # _internal目录
            os.path.join(base_dir, resource_path),  # 直接在根目录
        ]
        
        # 检查文件是否存在
        for path in possible_paths:
            if os.path.exists(path):
                return path
                
        # 如果没找到，记录错误并返回原始路径
        logging.error(f"在打包环境中未找到资源: {resource_path}")
        return None
    else:
        # 开发环境，使用包目录
        import importlib
        try:
            module = importlib.import_module(package)
            base_path = os.path.dirname(module.__file__)
            return os.path.join(base_path, resource_path)
        except (ImportError, AttributeError):
            # 如果无法导入模块，返回None
            logging.error(f"无法导入模块: {package}")
            return None


class PopupMonitor:
    """弹窗监控类，用于检测和处理应用中的弹窗"""
    def __init__(self, device_id=None):
        """
        初始化弹窗监控类
        :param device_id: 设备ID
        """
        self.device_id = device_id
        self.popup_config = PopupConfig()
        self.device = None
        self.monitor_thread = None
        self.is_monitoring = False
        self.logs = logging.getLogger("PopupMonitor")
    
    def connect_device(self, device_id=None):
        """
        连接设备
        :param device_id: 设备ID
        :return: 是否连接成功
        """
        try:
            device_id = device_id or self.device_id
            if not device_id:
                self.logs.error("未指定设备ID")
                return False
            
            self.device = u2.connect(device_id)
            self.device_id = device_id
            return True
        except Exception as e:
            self.logs.error(f"连接设备失败: {e}")
            return False
    
    def handle_popup(self, rule):
        """
        处理弹窗
        :param rule: 弹窗规则
        :return: 是否处理成功
        """
        try:
            if not self.device:
                if not self.connect_device():
                    return False
            
            rule_type = rule.get("type", "")
            
            # 根据规则类型处理弹窗
            if rule_type == "text":
                # 基于文本内容识别弹窗
                keywords = rule.get("keywords", [])
                action = rule.get("action", "click")
                target = rule.get("target", "")
                
                # 检查页面上是否有包含关键词的元素
                for keyword in keywords:
                    elements = self.device(text=keyword).exists
                    if elements:
                        self.logs.info(f"检测到弹窗: {rule.get('name')}")
                        
                        # 执行操作
                        if action == "click" and target:
                            # 点击指定文本的按钮
                            if self.device(text=target).exists:
                                self.device(text=target).click()
                                self.logs.info(f"点击了 '{target}' 按钮")
                                return True
                        elif action == "click_center":
                            # 点击弹窗中心位置
                            element = self.device(text=keyword)
                            element.click()
                            self.logs.info(f"点击了包含 '{keyword}' 的元素中心位置")
                            return True
                        elif action == "back":
                            # 返回键
                            self.device.press("back")
                            self.logs.info("按下返回键")
                            return True
            
            elif rule_type == "id":
                # 基于资源ID识别弹窗
                resource_id = rule.get("resource_id", "")
                action = rule.get("action", "click")
                
                if resource_id and self.device(resourceId=resource_id).exists:
                    self.logs.info(f"检测到弹窗: {rule.get('name')}")
                    
                    # 执行操作
                    if action == "click":
                        self.device(resourceId=resource_id).click()
                        self.logs.info(f"点击了资源ID为 '{resource_id}' 的元素")
                        return True
                    elif action == "back":
                        self.device.press("back")
                        self.logs.info("按下返回键")
                        return True
            
            elif rule_type == "xpath":
                # 基于XPath识别弹窗
                xpath = rule.get("xpath", "")
                action = rule.get("action", "click")
                
                if xpath and self.device.xpath(xpath).exists:
                    self.logs.info(f"检测到弹窗: {rule.get('name')}")
                    
                    # 执行操作
                    if action == "click":
                        self.device.xpath(xpath).click()
                        self.logs.info(f"点击了XPath为 '{xpath}' 的元素")
                        return True
                    elif action == "back":
                        self.device.press("back")
                        self.logs.info("按下返回键")
                        return True
            
            return False
        except Exception as e:
            self.logs.error(f"处理弹窗失败: {e}")
            return False
    
    def check_popups(self):
        """
        检查所有弹窗规则
        :return: 是否处理了任何弹窗
        """
        if not self.popup_config.is_enabled():
            return False
        
        if not self.device:
            if not self.connect_device():
                return False
        
        handled = False
        rules = self.popup_config.get_rules()
        
        for rule in rules:
            if self.handle_popup(rule):
                handled = True
                # 处理完一个弹窗后稍等片刻，避免连续操作
                time.sleep(0.5)
        
        return handled
    
    def monitor_popups(self):
        """监控弹窗的线程函数"""
        self.logs.info(f"开始监控设备 {self.device_id} 的弹窗")
        
        while self.is_monitoring:
            try:
                self.check_popups()
                # 按配置的间隔时间检查
                time.sleep(self.popup_config.get_check_interval())
            except Exception as e:
                self.logs.error(f"监控弹窗异常: {e}")
                time.sleep(1)  # 发生异常时短暂等待后继续
        
        self.logs.info(f"停止监控设备 {self.device_id} 的弹窗")
    
    def start_monitor(self, device_id=None):
        """
        启动弹窗监控
        :param device_id: 设备ID
        :return: 是否成功启动
        """
        if self.is_monitoring:
            self.logs.warning("弹窗监控已经在运行")
            return False
        
        # 如果提供了新的设备ID，则更新
        if device_id:
            self.device_id = device_id
        
        # 确保已连接设备
        if not self.device:
            if not self.connect_device():
                return False
        
        # 启动监控线程
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_popups)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        return True
    
    def stop_monitor(self):
        """
        停止弹窗监控
        :return: 是否成功停止
        """
        # 避免重复输出警告日志
        if not self.is_monitoring:
            # 只有在实际运行时才输出日志
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.logs.warning("弹窗监控未运行")
            return False
        
        self.is_monitoring = False
        if self.monitor_thread:
            # 等待线程结束
            if self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            self.monitor_thread = None
        
        return True
    
    def check_once(self, device_id=None, max_retries=None):
        """
        执行一次弹窗检查
        :param device_id: 设备ID
        :param max_retries: 最大重试次数，默认使用配置中的值
        :return: 是否处理了任何弹窗
        """
        # 如果提供了新的设备ID，则更新
        if device_id:
            self.device_id = device_id
        
        # 确保已连接设备
        if not self.device:
            if not self.connect_device():
                return False
        
        # 获取最大重试次数
        if max_retries is None:
            max_retries = self.popup_config.get_max_retries()
        
        # 执行多次检查，直到没有弹窗或达到最大重试次数
        handled = False
        retry_count = 0
        
        while retry_count < max_retries:
            if self.check_popups():
                handled = True
                retry_count += 1
                # 处理完弹窗后稍等片刻，避免连续操作
                time.sleep(0.5)
            else:
                # 如果没有检测到弹窗，则退出循环
                break
        
        return handled
    
    def set_logs(self, logs):
        """设置日志对象"""
        self.logs = logs


if __name__ == '__main__':
    # 1. 初始化 PopupMonitor
    device_id = "10AEA935LP000PU"  # 替换为你的设备ID
    monitor = PopupMonitor(device_id)

    # 2. 配置弹窗规则 (通常在 PopupConfig 中配置)
    popup_config = PopupConfig()

    # 添加一些示例规则
    popup_config.add_rule({
        "name": "个人信息弹窗",
        "type": "text",
        "keywords": ["同意"],
        "action": "click",
        "target": "同意",
        "enabled": True
    })

    popup_config.add_rule({
        "name": "权限请求弹窗",
        "type": "text",
        "keywords": ["允许", "拒绝", "权限"],
        "action": "click",
        "target": "允许",
        "enabled": True
    })

    popup_config.add_rule({
        "name": "广告弹窗",
        "type": "id",
        "resource_id": "com.example.ad:id/close_button",
        "action": "click",
        "enabled": True
    })

    popup_config.add_rule({
        "name": "更新提示",
        "type": "xpath",
        "xpath": "//*[@text='下次再说']",
        "action": "click",
        "enabled": True
    })

    # 3. 使用方式1: 启动持续监控
    monitor.start_monitor()

    i = 0
    # 模拟应用运行
    try:
        while True:
            # 这里可以放置你的主应用逻辑
            time.sleep(2)
            print(i)
            i += 1
    except KeyboardInterrupt:
        monitor.stop_monitor()
    pass
