"""
亮度测试API客户端库

这是一个用于与亮度调节自动化程序API服务交互的Python客户端库。
提供简洁易用的接口，支持所有预设配置和高级功能。

主要特性：
- 完整的API封装
- 预设配置快速调用
- 任务状态监控
- 错误处理和重试机制
- Python 3.7+ 兼容

使用示例：
    from brightness_client import BrightnessTestClient
    
    client = BrightnessTestClient()
    
    # 检查服务状态
    if client.is_service_available():
        # 添加内销OLED自动亮度测试
        client.add_内_OLED_自动()
        
        # 启动执行
        client.start_execution()
        
        # 等待完成
        client.wait_for_completion()

版本: 1.0.0
兼容性: Python 3.7+
"""

from .client import BrightnessTestClient
from .exceptions import (
    BrightnessClientError,
    APIConnectionError,
    APIRequestError,
    ServiceUnavailableError,
    ExecutionError
)
from .models import (
    HealthStatus,
    QueueStatus,
    ExecutionStatus,
    DeviceInfo
)

__version__ = "1.0.0"
__author__ = "Brightness Test Team"
__email__ = "<EMAIL>"

__all__ = [
    "BrightnessTestClient",
    "BrightnessClientError",
    "APIConnectionError", 
    "APIRequestError",
    "ServiceUnavailableError",
    "ExecutionError",
    "HealthStatus",
    "QueueStatus", 
    "ExecutionStatus",
    "DeviceInfo"
]
