"""
ExU2.py - 封装uiautomator2操作的工具类
提供基本的UI自动化操作功能，特别是包名验证和相关操作
"""
import time
import logging
import uiautomator2 as u2
from typing import Optional, Union, Tuple


class ExU2:
    """封装uiautomator2操作的工具类"""
    
    def __init__(self, device_id: Optional[str] = None, logger=None):
        """
        初始化U2操作类
        
        Args:
            device_id: 设备ID，如果为None则连接默认设备
            logger: 日志记录器，如果为None则创建新的日志记录器
        """
        self.device_id = device_id
        self.device = None
        self.connected = False
        
        # 设置日志记录器
        self.logger = logger or logging.getLogger("ExU2")
    
    def connect(self, device_id: Optional[str] = None) -> bool:
        """
        连接设备
        
        Args:
            device_id: 要连接的设备ID，如果为None则使用初始化时的设备ID
            
        Returns:
            bool: 连接是否成功
        """
        try:
            # 如果提供了新的device_id，则使用新的；否则使用初始化时的device_id
            dev_id = device_id or self.device_id
            
            # 尝试连接设备
            self.device = u2.connect(dev_id)
            self.connected = True
            self.device_id = dev_id
            self.logger.info(f"成功连接到设备: {dev_id}")
            return True
        except Exception as e:
            self.connected = False
            self.logger.error(f"连接设备失败: {e}")
            return False
    
    def ensure_connected(self) -> bool:
        """
        确保设备已连接，如果未连接则尝试连接
        
        Returns:
            bool: 设备是否已连接
        """
        if not self.connected or self.device is None:
            return self.connect()
        return True
    
    def get_current_package(self) -> Optional[str]:
        """
        获取当前前台应用的包名
        
        Returns:
            str: 当前前台应用的包名，失败时返回None
        """
        if not self.ensure_connected():
            return None
            
        try:
            current_info = self.device.app_current()
            return current_info.get("package")
        except Exception as e:
            self.logger.error(f"获取当前包名失败: {e}")
            return None
    
    def verify_package(self, expected_package: str, action_if_mismatch: bool = True) -> bool:
        """
        验证当前页面的包名是否与预期一致，不一致则可执行特定操作
        
        Args:
            expected_package: 预期的包名
            action_if_mismatch: 如果包名不匹配，是否执行特定操作
            
        Returns:
            bool: 包名是否匹配预期
        """
        current_package = self.get_current_package()
        if current_package is None:
            self.logger.error("无法获取当前包名")
            return False
            
        is_match = current_package == expected_package
            
        return is_match
    
    def perform_action_on_mismatch(self, offset_y=200):
        """
        当包名不匹配时执行的特定操作
        默认在屏幕中心点往上偏移 `offset_y` 的地方点击一下
    
        Args:
            offset_y (int): 点击位置相对于屏幕中心的 Y 轴偏移量（默认 200）
        """
        if not self.ensure_connected():
            return
    
        try:
            # 获取屏幕尺寸
            screen_info = self.device.window_size()
            width, height = screen_info[0], screen_info[1]
    
            # 计算点击位置（屏幕中心点往上偏移 `offset_y`）
            x = width // 2
            y = height // 2 - offset_y
    
            # 执行点击
            self.logger.info(f"执行点击操作: ({x}, {y})")
            self.device.click(x, y)
        except Exception as e:
            self.logger.error(f"执行特定操作失败: {e}")
    
    def contains_text(self, text: str, timeout: float = 1.0) -> bool:
        """
        判断当前页面是否包含指定文字
        
        Args:
            text: 要查找的文字
            timeout: 超时时间(秒)，默认1秒
            
        Returns:
            bool: 页面是否包含指定文字
        """
        if not self.ensure_connected():
            return False
            
        try:
            self.logger.info(f"检查页面是否包含文字: '{text}'")
            result = self.device(text=text).exists(timeout=timeout)
            
            if result:
                self.logger.info(f"页面包含文字: '{text}'")
            else:
                self.logger.info(f"页面不包含文字: '{text}'")
                
            return result
        except Exception as e:
            self.logger.error(f"检查文字时发生错误: {e}")
            return False
    
    def click(self, x: Union[int, float], y: Union[int, float]):
        """
        点击屏幕指定位置
        
        Args:
            x: X坐标或屏幕宽度的比例(0.0-1.0)
            y: Y坐标或屏幕高度的比例(0.0-1.0)
        """
        if not self.ensure_connected():
            return
            
        try:
            # 如果x, y是比例值(0.0-1.0)，则转换为实际坐标
            if 0 <= x <= 1.0 and isinstance(x, float):
                screen_width = self.device.window_size()[0]
                x = int(x * screen_width)
                
            if 0 <= y <= 1.0 and isinstance(y, float):
                screen_height = self.device.window_size()[1]
                y = int(y * screen_height)
                
            self.device.click(x, y)
            self.logger.info(f"点击坐标: ({x}, {y})")
        except Exception as e:
            self.logger.error(f"点击操作失败: {e}")
    
    def swipe(self, fx: Union[int, float], fy: Union[int, float], 
              tx: Union[int, float], ty: Union[int, float], duration: float = 0.5):
        """
        从一个位置滑动到另一个位置
        
        Args:
            fx: 起始X坐标或屏幕宽度的比例(0.0-1.0)
            fy: 起始Y坐标或屏幕高度的比例(0.0-1.0)
            tx: 目标X坐标或屏幕宽度的比例(0.0-1.0)
            ty: 目标Y坐标或屏幕高度的比例(0.0-1.0)
            duration: 滑动持续时间(秒)
        """
        if not self.ensure_connected():
            return
            
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = self.device.window_size()
            
            # 转换坐标比例为实际坐标
            if 0 <= fx <= 1.0 and isinstance(fx, float):
                fx = int(fx * screen_width)
            if 0 <= fy <= 1.0 and isinstance(fy, float):
                fy = int(fy * screen_height)
            if 0 <= tx <= 1.0 and isinstance(tx, float):
                tx = int(tx * screen_width)
            if 0 <= ty <= 1.0 and isinstance(ty, float):
                ty = int(ty * screen_height)
                
            self.device.swipe(fx, fy, tx, ty, duration=duration)
            self.logger.info(f"滑动: 从({fx}, {fy})到({tx}, {ty}), 持续{duration}秒")
        except Exception as e:
            self.logger.error(f"滑动操作失败: {e}")
    
    def swipe_up(self, distance: Optional[int] = None, duration: float = 0.5):
        """
        向上滑动屏幕
        
        Args:
            distance: 滑动距离，None则使用默认值（屏幕高度的1/3）
            duration: 滑动持续时间(秒)
        """
        if not self.ensure_connected():
            return
            
        try:
            width, height = self.device.window_size()
            center_x = width // 2
            
            # 如果没有指定滑动距离，则使用屏幕高度的1/3
            if distance is None:
                distance = height // 3
                
            start_y = height // 2 + distance // 2
            end_y = height // 2 - distance // 2
            
            self.device.swipe(center_x, start_y, center_x, end_y, duration=duration)
            self.logger.info(f"向上滑动: 距离{distance}像素, 持续{duration}秒")
        except Exception as e:
            self.logger.error(f"向上滑动失败: {e}")
    
    def swipe_down(self, distance: Optional[int] = None, duration: float = 0.5):
        """
        向下滑动屏幕
        
        Args:
            distance: 滑动距离，None则使用默认值（屏幕高度的1/3）
            duration: 滑动持续时间(秒)
        """
        if not self.ensure_connected():
            return
            
        try:
            width, height = self.device.window_size()
            center_x = width // 2
            
            # 如果没有指定滑动距离，则使用屏幕高度的1/3
            if distance is None:
                distance = height // 3
                
            start_y = height // 2 - distance // 2
            end_y = height // 2 + distance // 2
            
            self.device.swipe(center_x, start_y, center_x, end_y, duration=duration)
            self.logger.info(f"向下滑动: 距离{distance}像素, 持续{duration}秒")
        except Exception as e:
            self.logger.error(f"向下滑动失败: {e}")
    
    def swipe_left(self, distance: Optional[int] = None, duration: float = 0.5):
        """
        向左滑动屏幕
        
        Args:
            distance: 滑动距离，None则使用默认值（屏幕宽度的2/3）
            duration: 滑动持续时间(秒)
        """
        if not self.ensure_connected():
            return
            
        try:
            width, height = self.device.window_size()
            center_y = height // 2
            
            # 如果没有指定滑动距离，则使用屏幕宽度的2/3
            if distance is None:
                distance = width * 2 // 3
                
            start_x = width // 2 + distance // 2
            end_x = width // 2 - distance // 2
            
            self.device.swipe(start_x, center_y, end_x, center_y, duration=duration)
            self.logger.info(f"向左滑动: 距离{distance}像素, 持续{duration}秒")
        except Exception as e:
            self.logger.error(f"向左滑动失败: {e}")
    
    def swipe_right(self, distance: Optional[int] = None, duration: float = 0.5):
        """
        向右滑动屏幕
        
        Args:
            distance: 滑动距离，None则使用默认值（屏幕宽度的2/3）
            duration: 滑动持续时间(秒)
        """
        if not self.ensure_connected():
            return
            
        try:
            width, height = self.device.window_size()
            center_y = height // 2
            
            # 如果没有指定滑动距离，则使用屏幕宽度的2/3
            if distance is None:
                distance = width * 2 // 3
                
            start_x = width // 2 - distance // 2
            end_x = width // 2 + distance // 2
            
            self.device.swipe(start_x, center_y, end_x, center_y, duration=duration)
            self.logger.info(f"向右滑动: 距离{distance}像素, 持续{duration}秒")
        except Exception as e:
            self.logger.error(f"向右滑动失败: {e}")
    
    def wait_for_package(self, package_name: str, timeout: float = 10.0) -> bool:
        """
        等待指定包名的应用出现在前台
        
        Args:
            package_name: 要等待的包名
            timeout: 超时时间(秒)
            
        Returns:
            bool: 是否成功等待到指定包名
        """
        if not self.ensure_connected():
            return False
            
        try:
            start_time = time.time()
            while time.time() - start_time < timeout:
                current_package = self.get_current_package()
                if current_package == package_name:
                    self.logger.info(f"成功等待到包名: {package_name}")
                    return True
                    
                time.sleep(0.5)
                
            self.logger.warning(f"等待包名超时: {package_name}")
            return False
        except Exception as e:
            self.logger.error(f"等待包名时发生错误: {e}")
            return False
    
    def find_element_by_text(self, text: str, timeout: float = 5.0) -> Optional[u2.UiObject]:
        """
        通过文本查找元素
        
        Args:
            text: 要查找的文本
            timeout: 超时时间(秒)
            
        Returns:
            UiObject: 找到的元素，找不到则返回None
        """
        if not self.ensure_connected():
            return None
            
        try:
            self.logger.info(f"查找文本: '{text}'")
            if self.device(text=text).exists(timeout=timeout):
                return self.device(text=text)
            
            self.logger.warning(f"未找到文本: '{text}'")
            return None
        except Exception as e:
            self.logger.error(f"查找文本时发生错误: {e}")
            return None
    
    def find_element_by_id(self, resource_id: str, timeout: float = 5.0) -> Optional[u2.UiObject]:
        """
        通过资源ID查找元素
        
        Args:
            resource_id: 要查找的资源ID
            timeout: 超时时间(秒)
            
        Returns:
            UiObject: 找到的元素，找不到则返回None
        """
        if not self.ensure_connected():
            return None
            
        try:
            self.logger.info(f"查找资源ID: '{resource_id}'")
            if self.device(resourceId=resource_id).exists(timeout=timeout):
                return self.device(resourceId=resource_id)
            
            self.logger.warning(f"未找到资源ID: '{resource_id}'")
            return None
        except Exception as e:
            self.logger.error(f"查找资源ID时发生错误: {e}")
            return None
    
    def press_back(self):
        """按下返回键"""
        if not self.ensure_connected():
            return
            
        try:
            self.device.press("back")
            self.logger.info("按下返回键")
        except Exception as e:
            self.logger.error(f"按下返回键失败: {e}")
    
    def press_home(self):
        """按下Home键"""
        if not self.ensure_connected():
            return
            
        try:
            self.device.press("home")
            self.logger.info("按下Home键")
        except Exception as e:
            self.logger.error(f"按下Home键失败: {e}")
    
    def press_recent(self):
        """按下最近任务键"""
        if not self.ensure_connected():
            return
            
        try:
            self.device.press("recent")
            self.logger.info("按下最近任务键")
        except Exception as e:
            self.logger.error(f"按下最近任务键失败: {e}") 