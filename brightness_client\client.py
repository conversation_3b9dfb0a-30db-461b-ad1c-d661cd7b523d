"""
亮度测试API客户端

主要的客户端类，提供完整的API封装和高级功能。
"""

import time
import logging
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin

try:
    import requests
except ImportError:
    raise ImportError("requests库未安装，请运行: pip install requests")

from .exceptions import (
    BrightnessClientError,
    APIConnectionError,
    APIRequestError,
    ServiceUnavailableError,
    ExecutionError,
    TimeoutError,
    ValidationError
)
from .models import (
    HealthStatus,
    QueueStatus,
    ExecutionStatus,
    DeviceInfo,
    ExecutionError as ExecutionErrorModel
)


class BrightnessTestClient:
    """
    亮度测试API客户端
    
    提供与亮度调节自动化程序API服务交互的完整功能。
    支持所有预设配置、任务监控、错误处理等高级特性。
    
    使用示例:
        client = BrightnessTestClient()
        
        # 检查服务状态
        if client.is_service_available():
            # 添加测试队列
            client.add_内_OLED_自动()
            
            # 启动执行
            client.start_execution()
            
            # 等待完成
            result = client.wait_for_completion(timeout=300)
    """
    
    def __init__(self, base_url: str = "http://127.0.0.1:9080", 
                 timeout: int = 30, max_retries: int = 3,
                 logger: Optional[logging.Logger] = None):
        """
        初始化客户端
        
        Args:
            base_url: API服务器基础URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            logger: 日志记录器，如果为None则创建默认记录器
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api"
        self.timeout = timeout
        self.max_retries = max_retries
        
        # 设置日志
        self.logger = logger or self._create_default_logger()
        
        # 会话对象，用于连接复用
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'BrightnessTestClient/1.0.0'
        })
        
        self.logger.info(f"客户端初始化完成，API地址: {self.api_url}")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger('brightness_client')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _make_request(self, method: str, endpoint: str,
                     data: Optional[Dict[str, Any]] = None,
                     params: Optional[Dict[str, Any]] = None,
                     enable_retry: bool = True) -> Dict[str, Any]:
        """
        发送HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            params: URL参数
            enable_retry: 是否启用重试机制，默认True

        Returns:
            API响应数据

        Raises:
            APIConnectionError: 连接失败
            APIRequestError: 请求失败
        """
        url = urljoin(self.api_url + '/', endpoint.lstrip('/'))

        # 根据enable_retry参数决定重试次数
        max_attempts = self.max_retries + 1 if enable_retry else 1

        for attempt in range(max_attempts):
            try:
                self.logger.debug(f"发送请求: {method} {url}")
                
                response = self.session.request(
                    method=method,
                    url=url,
                    json=data,
                    params=params,
                    timeout=self.timeout
                )
                
                # 检查响应状态
                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == "success":
                        return result.get("data", {})
                    else:
                        raise APIRequestError(
                            result.get("message", "API请求失败"),
                            response.status_code,
                            result
                        )
                else:
                    raise APIRequestError(
                        f"HTTP {response.status_code}: {response.text}",
                        response.status_code
                    )
                    
            except requests.exceptions.ConnectionError as e:
                if attempt == max_attempts - 1:  # 最后一次尝试
                    raise APIConnectionError(f"连接失败: {str(e)}", url)
                if enable_retry:
                    self.logger.warning(f"连接失败，重试 {attempt + 1}/{max_attempts}")
                    time.sleep(1)

            except requests.exceptions.Timeout as e:
                if attempt == max_attempts - 1:  # 最后一次尝试
                    raise TimeoutError(f"请求超时: {str(e)}", self.timeout)
                if enable_retry:
                    self.logger.warning(f"请求超时，重试 {attempt + 1}/{max_attempts}")
                    time.sleep(1)
                
            except requests.exceptions.RequestException as e:
                raise APIConnectionError(f"请求异常: {str(e)}", url)
    
    # ==================== 基础API方法 ====================
    
    def health_check(self) -> HealthStatus:
        """
        健康检查
        
        Returns:
            HealthStatus: 健康状态信息
        """
        data = self._make_request('GET', '/health')
        return HealthStatus(data)
    
    def is_service_available(self) -> bool:
        """
        检查服务是否可用
        
        Returns:
            bool: 服务是否可用
        """
        try:
            health = self.health_check()
            return health.is_healthy
        except Exception as e:
            self.logger.warning(f"服务不可用: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            Dict: 系统状态信息
        """
        return self._make_request('GET', '/status')
    
    def get_devices(self) -> DeviceInfo:
        """
        获取设备列表
        
        Returns:
            DeviceInfo: 设备信息
        """
        data = self._make_request('GET', '/devices')
        return DeviceInfo(data)
    
    def get_presets(self) -> Dict[str, List[str]]:
        """
        获取预设列表
        
        Returns:
            Dict: 预设配置字典，键为版本，值为预设名称列表
        """
        data = self._make_request('GET', '/presets')
        return data.get('presets', {})
    
    def get_queue(self) -> QueueStatus:
        """
        获取队列状态
        
        Returns:
            QueueStatus: 队列状态信息
        """
        data = self._make_request('GET', '/queue')
        return QueueStatus(data)
    
    def start_execution(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """
        启动队列执行
        
        Args:
            device_id: 指定设备ID，可选
            
        Returns:
            Dict: 启动结果
        """
        data = {}
        if device_id:
            data['device_id'] = device_id
            
        return self._make_request('POST', '/queue/start', data)
    
    def stop_execution(self) -> Dict[str, Any]:
        """
        停止队列执行
        
        Returns:
            Dict: 停止结果
        """
        return self._make_request('POST', '/queue/stop')
    
    def clear_queue(self) -> Dict[str, Any]:
        """
        清空队列

        Returns:
            Dict: 清空结果
        """
        return self._make_request('DELETE', '/queue/clear')

    # ==================== 任务监控方法 ====================

    def get_execution_status(self, enable_retry: bool = False) -> ExecutionStatus:
        """
        获取执行状态

        Args:
            enable_retry: 是否启用重试机制，默认False（适合轮询场景）

        Returns:
            ExecutionStatus: 执行状态信息
        """
        data = self._make_request('GET', '/execution/status', enable_retry=enable_retry)
        return ExecutionStatus(data)

    def get_execution_errors(self) -> Dict[str, Any]:
        """
        获取执行错误

        Returns:
            Dict: 错误信息
        """
        return self._make_request('GET', '/execution/errors')

    def is_execution_completed(self) -> Dict[str, Any]:
        """
        检查执行是否完成

        Returns:
            Dict: 完成状态信息
        """
        return self._make_request('GET', '/execution/completed')

    def wait_for_completion(self, timeout: int = 600, poll_interval: int = 5) -> ExecutionStatus:
        """
        等待任务完成

        Args:
            timeout: 超时时间（秒）
            poll_interval: 轮询间隔（秒）

        Returns:
            ExecutionStatus: 最终执行状态

        Raises:
            TimeoutError: 超时
            ExecutionError: 执行失败
        """
        start_time = time.time()
        self.logger.info(f"开始等待任务完成，超时时间: {timeout}秒")

        while time.time() - start_time < timeout:
            try:
                status = self.get_execution_status()

                self.logger.debug(f"执行状态: {status.summary.execution_status}, "
                                f"进度: {status.summary.completed_queues}/{status.summary.total_queues}")

                if status.summary.is_completed:
                    if status.summary.execution_status == "completed":
                        self.logger.info("任务执行完成")
                        return status
                    elif status.summary.execution_status == "failed":
                        # 获取错误信息
                        errors = self.get_execution_errors()
                        raise ExecutionError(
                            "任务执行失败",
                            status.summary.execution_status,
                            errors
                        )
                    elif status.summary.execution_status == "stopped":
                        self.logger.info("任务被停止")
                        return status

                time.sleep(poll_interval)

            except (APIConnectionError, APIRequestError) as e:
                self.logger.warning(f"获取执行状态失败: {e}")
                time.sleep(poll_interval)

        raise TimeoutError(f"等待任务完成超时（{timeout}秒）", timeout)

    def monitor_execution(self, callback=None, poll_interval: int = 5) -> ExecutionStatus:
        """
        监控执行状态（持续监控直到完成）

        Args:
            callback: 状态更新回调函数，接收ExecutionStatus参数
            poll_interval: 轮询间隔（秒）

        Returns:
            ExecutionStatus: 最终执行状态
        """
        self.logger.info("开始监控任务执行")

        while True:
            try:
                status = self.get_execution_status()

                if callback:
                    callback(status)

                if status.summary.is_completed:
                    self.logger.info(f"任务监控结束，最终状态: {status.summary.execution_status}")
                    return status

                time.sleep(poll_interval)

            except KeyboardInterrupt:
                self.logger.info("监控被用户中断")
                break
            except Exception as e:
                self.logger.error(f"监控过程中出现错误: {e}")
                time.sleep(poll_interval)

    # ==================== 5.0版本预设方法 ====================

    def add_内_OLED_自动(self) -> Dict[str, Any]:
        """
        添加内销OLED自动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "内_OLED_自动"}
        self.logger.info("添加内销OLED自动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_内_OLED_手动(self) -> Dict[str, Any]:
        """
        添加内销OLED手动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "内_OLED_手动"}
        self.logger.info("添加内销OLED手动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_外_OLED_自动(self) -> Dict[str, Any]:
        """
        添加外销OLED自动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "外_OLED_自动"}
        self.logger.info("添加外销OLED自动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_外_OLED_手动(self) -> Dict[str, Any]:
        """
        添加外销OLED手动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "外_OLED_手动"}
        self.logger.info("添加外销OLED手动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_内_LCD_自动(self) -> Dict[str, Any]:
        """
        添加内销LCD自动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "内_LCD_自动"}
        self.logger.info("添加内销LCD自动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_内_LCD_手动(self) -> Dict[str, Any]:
        """
        添加内销LCD手动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "内_LCD_手动"}
        self.logger.info("添加内销LCD手动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_外_LCD_自动(self) -> Dict[str, Any]:
        """
        添加外销LCD自动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "外_LCD_自动"}
        self.logger.info("添加外销LCD自动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_外_LCD_手动(self) -> Dict[str, Any]:
        """
        添加外销LCD手动亮度测试队列

        Returns:
            Dict: 添加结果
        """
        data = {"preset": "外_LCD_手动"}
        self.logger.info("添加外销LCD手动亮度测试队列")
        return self._make_request('POST', '/queue/add/5.0', data)

    # ==================== 4.0版本预设方法 ====================

    def add_内销自动(self, brightness_values: List[int]) -> Dict[str, Any]:
        """
        添加内销自动亮度测试队列（4.0版本）

        Args:
            brightness_values: 亮度值列表，例如 [30, 100, 300]

        Returns:
            Dict: 添加结果
        """
        if not brightness_values:
            raise ValidationError("亮度值列表不能为空", "brightness_values")

        data = {
            "preset": "内销自动",
            "brightness_values": brightness_values
        }
        self.logger.info(f"添加内销自动亮度测试队列，亮度值: {brightness_values}")
        return self._make_request('POST', '/queue/add/4.0', data)

    def add_内销手动(self, brightness_values: List[int]) -> Dict[str, Any]:
        """
        添加内销手动亮度测试队列（4.0版本）

        Args:
            brightness_values: 亮度值列表，例如 [30, 100, 300]

        Returns:
            Dict: 添加结果
        """
        if not brightness_values:
            raise ValidationError("亮度值列表不能为空", "brightness_values")

        data = {
            "preset": "内销手动",
            "brightness_values": brightness_values
        }
        self.logger.info(f"添加内销手动亮度测试队列，亮度值: {brightness_values}")
        return self._make_request('POST', '/queue/add/4.0', data)

    def add_外销自动(self, brightness_values: List[int]) -> Dict[str, Any]:
        """
        添加外销自动亮度测试队列（4.0版本）

        Args:
            brightness_values: 亮度值列表，例如 [30, 100, 300]

        Returns:
            Dict: 添加结果
        """
        if not brightness_values:
            raise ValidationError("亮度值列表不能为空", "brightness_values")

        data = {
            "preset": "外销自动",
            "brightness_values": brightness_values
        }
        self.logger.info(f"添加外销自动亮度测试队列，亮度值: {brightness_values}")
        return self._make_request('POST', '/queue/add/4.0', data)

    def add_外销手动(self, brightness_values: List[int]) -> Dict[str, Any]:
        """
        添加外销手动亮度测试队列（4.0版本）

        Args:
            brightness_values: 亮度值列表，例如 [30, 100, 300]

        Returns:
            Dict: 添加结果
        """
        if not brightness_values:
            raise ValidationError("亮度值列表不能为空", "brightness_values")

        data = {
            "preset": "外销手动",
            "brightness_values": brightness_values
        }
        self.logger.info(f"添加外销手动亮度测试队列，亮度值: {brightness_values}")
        return self._make_request('POST', '/queue/add/4.0', data)

    # ==================== 高级功能方法 ====================

    def batch_add_presets_5_0(self, presets: List[str]) -> List[Dict[str, Any]]:
        """
        批量添加5.0版本预设

        Args:
            presets: 预设名称列表

        Returns:
            List[Dict]: 添加结果列表
        """
        results = []
        for preset in presets:
            try:
                data = {"preset": preset}
                result = self._make_request('POST', '/queue/add/5.0', data)
                results.append({"preset": preset, "success": True, "result": result})
                self.logger.info(f"成功添加预设: {preset}")
            except Exception as e:
                results.append({"preset": preset, "success": False, "error": str(e)})
                self.logger.error(f"添加预设失败 {preset}: {e}")

        return results

    def batch_add_presets_4_0(self, presets_with_brightness: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量添加4.0版本预设

        Args:
            presets_with_brightness: 预设配置列表，格式: [{"preset": "内销自动", "brightness_values": [30, 100, 300]}]

        Returns:
            List[Dict]: 添加结果列表
        """
        results = []
        for config in presets_with_brightness:
            try:
                preset = config.get("preset")
                brightness_values = config.get("brightness_values")

                if not preset or not brightness_values:
                    raise ValidationError("预设名称和亮度值都是必需的")

                data = {
                    "preset": preset,
                    "brightness_values": brightness_values
                }
                result = self._make_request('POST', '/queue/add/4.0', data)
                results.append({"preset": preset, "success": True, "result": result})
                self.logger.info(f"成功添加预设: {preset}")
            except Exception as e:
                results.append({"preset": config.get("preset", "unknown"), "success": False, "error": str(e)})
                self.logger.error(f"添加预设失败 {config}: {e}")

        return results

    def add_custom_queue_5_0(self, apps: List[str], auto_brightness: bool = True,
                           preset_name: str = "自定义") -> Dict[str, Any]:
        """
        添加自定义5.0版本队列

        Args:
            apps: 应用列表
            auto_brightness: 是否自动亮度
            preset_name: 预设名称

        Returns:
            Dict: 添加结果
        """
        if not apps:
            raise ValidationError("应用列表不能为空", "apps")

        data = {
            "apps": apps,
            "auto_brightness": auto_brightness,
            "preset": preset_name
        }
        self.logger.info(f"添加自定义5.0队列: {preset_name}, 应用数量: {len(apps)}")
        return self._make_request('POST', '/queue/add/5.0', data)

    def add_custom_queue_4_0(self, apps: List[str], brightness_values: List[int],
                           auto_brightness: bool = True, preset_name: str = "自定义") -> Dict[str, Any]:
        """
        添加自定义4.0版本队列

        Args:
            apps: 应用列表
            brightness_values: 亮度值列表
            auto_brightness: 是否自动亮度
            preset_name: 预设名称

        Returns:
            Dict: 添加结果
        """
        if not apps:
            raise ValidationError("应用列表不能为空", "apps")
        if not brightness_values:
            raise ValidationError("亮度值列表不能为空", "brightness_values")

        data = {
            "apps": apps,
            "brightness_values": brightness_values,
            "auto_brightness": auto_brightness,
            "preset": preset_name
        }
        self.logger.info(f"添加自定义4.0队列: {preset_name}, 应用数量: {len(apps)}")
        return self._make_request('POST', '/queue/add/4.0', data)

    # ==================== 便捷方法 ====================

    def quick_test_内_OLED(self, include_manual: bool = True) -> Dict[str, Any]:
        """
        快速测试内销OLED（自动+手动）

        Args:
            include_manual: 是否包含手动测试

        Returns:
            Dict: 执行结果摘要
        """
        self.logger.info("开始快速内销OLED测试")

        # 清空现有队列
        self.clear_queue()

        # 添加自动测试
        self.add_内_OLED_自动()

        # 添加手动测试（如果需要）
        if include_manual:
            self.add_内_OLED_手动()

        # 启动执行
        self.start_execution()

        # 等待完成
        result = self.wait_for_completion()

        return {
            "test_type": "内销OLED",
            "include_manual": include_manual,
            "execution_status": result.summary.execution_status,
            "completed_queues": result.summary.completed_queues,
            "total_queues": result.summary.total_queues,
            "duration_seconds": result.summary.duration_seconds
        }

    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务完整信息

        Returns:
            Dict: 服务信息汇总
        """
        try:
            health = self.health_check()
            status = self.get_status()
            devices = self.get_devices()
            presets = self.get_presets()
            queue = self.get_queue()

            return {
                "service_available": True,
                "health": {
                    "status": health.status,
                    "uptime": health.uptime,
                    "device_connected": health.device_connected,
                    "has_errors": health.has_errors
                },
                "devices": {
                    "count": devices.device_count,
                    "list": devices.devices
                },
                "presets": presets,
                "queue": {
                    "count": queue.queue_count,
                    "is_running": queue.is_running,
                    "is_empty": queue.is_empty
                }
            }
        except Exception as e:
            return {
                "service_available": False,
                "error": str(e)
            }

    def close(self):
        """
        关闭客户端，清理资源
        """
        if hasattr(self, 'session'):
            self.session.close()
        self.logger.info("客户端已关闭")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
