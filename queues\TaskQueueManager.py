import datetime
import threading
import time
from typing import List, Optional, Union, Dict, Any
from enum import Enum
from queues.TestQueue import TestQueue
from queues.TestFourQueue import TestFourQueue
from utils.ExcelSave import ExcelSave
from utils.adb.ExAdb import ExAdb


class ExecutionStatus(Enum):
    """执行状态枚举"""
    IDLE = "idle"                    # 空闲状态
    RUNNING = "running"              # 执行中
    COMPLETED = "completed"          # 已完成
    FAILED = "failed"                # 执行失败
    STOPPED = "stopped"              # 用户停止


class QueueStatus(Enum):
    """单个队列状态枚举"""
    PENDING = "pending"              # 等待执行
    RUNNING = "running"              # 执行中
    COMPLETED = "completed"          # 已完成
    FAILED = "failed"                # 执行失败
    SKIPPED = "skipped"              # 跳过（因前面失败或停止）


class TaskQueueManager:
    """任务队列管理器，用于管理多个TestQueue的顺序执行"""

    def __init__(self, logs=None):
        """
        初始化任务队列管理器

        Args:
            logs: 日志记录器实例
        """
        self.queues: List[Union[TestQueue, TestFourQueue]] = []
        self.is_running = False
        self.should_stop = False
        self.current_queue_index = -1
        self.execution_thread = None
        self.logs = logs
        self.on_progress_callback = None
        self.on_queue_finished_callback = None
        self.on_all_finished_callback = None
        self.adb = ExAdb()

        # 新增：执行状态跟踪
        self.execution_status = ExecutionStatus.IDLE
        self.queue_statuses: List[QueueStatus] = []
        self.execution_errors: List[Dict[str, Any]] = []
        self.execution_start_time = None
        self.execution_end_time = None
        self.last_error = None
    
    def add_queue(self, test_queue: Union[TestQueue, TestFourQueue]) -> bool:
        """
        将测试队列添加到执行列表中

        Args:
            test_queue: 要添加的测试队列实例

        Returns:
            bool: 是否添加成功
        """
        if test_queue is None:
            if self.logs:
                self.logs.error("无法添加空队列")
            return False

        self.queues.append(test_queue)
        self.queue_statuses.append(QueueStatus.PENDING)
        if self.logs:
            self.logs.info(f"已将队列 '{test_queue.get_queue_title()}' 添加到执行队列")
        return True
    
    def clear(self) -> None:
        """清空所有待执行的队列"""
        if self.is_running:
            if self.logs:
                self.logs.warning("无法在执行过程中清空队列")
            return

        self.queues.clear()
        self.queue_statuses.clear()
        self.execution_errors.clear()
        self.current_queue_index = -1
        self.execution_status = ExecutionStatus.IDLE
        self.execution_start_time = None
        self.execution_end_time = None
        self.last_error = None
        if self.logs:
            self.logs.info("已清空执行队列")
    
    def get_queue_count(self) -> int:
        """获取队列数量"""
        return len(self.queues)
    
    def get_queue_titles(self) -> List[str]:
        """获取所有队列的标题（优化版本，确保非阻塞）"""
        try:
            return [q.get_queue_title() for q in self.queues]
        except Exception:
            # 如果出现异常，返回空列表
            return []
    
    def is_empty(self) -> bool:
        """判断队列是否为空"""
        return len(self.queues) == 0
    
    def set_callbacks(self, on_progress=None, on_queue_finished=None, on_all_finished=None):
        """
        设置回调函数，用于通知UI进度和状态
        
        Args:
            on_progress: 进度回调函数，参数为(当前队列索引, 队列总数, 当前队列进度)
            on_queue_finished: 单个队列完成回调函数，参数为(队列索引, 队列标题)
            on_all_finished: 所有队列完成回调函数，无参数
        """
        self.on_progress_callback = on_progress
        self.on_queue_finished_callback = on_queue_finished
        self.on_all_finished_callback = on_all_finished
    
    def execute_all(self, device_id: str, *args, **kwargs) -> bool:
        """
        依次执行所有队列

        Args:
            device_id: 设备ID
            *args, **kwargs: 传递给 TestQueue.start_queue 的其他参数

        Returns:
            bool: 是否成功启动执行
        """
        if self.is_running:
            if self.logs:
                self.logs.warning("队列管理器已在运行中")
            return False

        if len(self.queues) == 0:
            if self.logs:
                self.logs.warning("执行队列为空")
            return False

        # 重置状态
        self.is_running = True
        self.should_stop = False
        self.current_queue_index = -1
        self.execution_status = ExecutionStatus.RUNNING
        self.execution_errors.clear()
        self.execution_start_time = datetime.datetime.now()
        self.execution_end_time = None
        self.last_error = None

        # 重置所有队列状态为PENDING
        self.queue_statuses = [QueueStatus.PENDING] * len(self.queues)

        # 同步执行队列（阻塞式执行，适合GUI调用）
        self._execute_queues_thread(device_id, **kwargs)
        return True
    
    def _execute_queues_thread(self, device_id: str, **kwargs):
        """队列执行线程函数"""
        if self.logs:
            self.logs.info(f"开始执行队列，共 {len(self.queues)} 个队列")
        print(f"开始执行队列，共 {len(self.queues)} 个队列")

        try:
            device_list = self.adb.get_device_list()
            if len(device_list) < 1:
                self._record_error(-1, "系统检查", Exception("未找到连接的设备"))
                self.execution_status = ExecutionStatus.FAILED
                self.execution_end_time = datetime.datetime.now()
                return False

            filename = self.get_new_file_name(device_id=device_id, formatted_datetime=None, list_len=len(self.queues))
            has_error = False

            # 逐个执行队列
            for i, queue in enumerate(self.queues):
                # 检查是否应该停止执行
                if self.should_stop:
                    if self.logs:
                        self.logs.info("执行队列已中断")
                    # 将剩余队列标记为跳过
                    for j in range(i, len(self.queues)):
                        if j < len(self.queue_statuses):
                            self.queue_statuses[j] = QueueStatus.SKIPPED
                    self.execution_status = ExecutionStatus.STOPPED
                    break

                # 设置当前队列为运行状态
                self.current_queue_index = i
                if i < len(self.queue_statuses):
                    self.queue_statuses[i] = QueueStatus.RUNNING

                queue.start_brightness_thread()
                queue.set_file_name(filename)
                time.sleep(10)

                # 记录开始执行
                queue_title = queue.get_queue_title()
                if self.logs:
                    self.logs.info(f"正在执行队列 [{i+1}/{len(self.queues)}]: {queue_title}")

                # 定义进度条更新回调函数，转发队列进度到全局进度
                original_progress_bar = queue.progress_bar

                def progress_callback(value):
                    if original_progress_bar:
                        original_progress_bar.set(value)
                    if self.on_progress_callback:
                        self.on_progress_callback(i, len(self.queues), value)

                # 替换进度条
                custom_progress_bar = type('ProgressBarAdapter', (), {'set': progress_callback})
                queue.set_progress_bar(custom_progress_bar)

                try:
                    # 执行队列
                    queue.start_test_threads(sheet_name=queue_title, add_separator=True, is_headers=False)

                    # 标记队列完成
                    if i < len(self.queue_statuses):
                        self.queue_statuses[i] = QueueStatus.COMPLETED

                    # 通知单个队列完成
                    if self.on_queue_finished_callback:
                        self.on_queue_finished_callback(i, queue_title)

                except Exception as e:
                    # 记录错误并标记队列失败
                    self._record_error(i, queue_title, e)
                    if i < len(self.queue_statuses):
                        self.queue_statuses[i] = QueueStatus.FAILED
                    has_error = True

                finally:
                    # 恢复原始进度条
                    queue.set_progress_bar(original_progress_bar)
                    queue.stop_brightness_thread()

                    # 添加元数据
                    try:
                        for device_id in device_list:
                            excel_model = ExcelSave(device_id=device_id, filename=queue.get_file_name(),
                                                    sheet_name=queue_title, append_mode=True)
                            excel_model.add_metadata_to_sheet(sheet_name=queue_title)
                            excel_model.save(queue.get_file_name())
                            break
                    except Exception as e:
                        self._record_error(i, f"{queue_title}(保存)", e)

            # 设置最终执行状态
            if self.execution_status == ExecutionStatus.RUNNING:
                if has_error:
                    self.execution_status = ExecutionStatus.FAILED
                else:
                    self.execution_status = ExecutionStatus.COMPLETED

        except Exception as e:
            # 处理整体执行异常
            self._record_error(-1, "执行线程", e)
            self.execution_status = ExecutionStatus.FAILED

        finally:
            # 执行完成
            self.current_queue_index = -1
            self.is_running = False
            self.execution_end_time = datetime.datetime.now()

            if self.logs:
                self.logs.info(f"队列执行结束，状态: {self.execution_status.value}")

            # 通知所有队列完成
            if self.on_all_finished_callback:
                self.on_all_finished_callback()
    
    def stop(self):
        """停止队列执行"""
        if not self.is_running:
            return

        self.should_stop = True
        if self.logs:
            self.logs.info("正在停止队列执行...")

        # 如果正在执行，等待当前队列完成后停止
        # 状态将在执行线程中设置为STOPPED
    
    def get_current_queue_index(self) -> int:
        """获取当前正在执行的队列索引"""
        return self.current_queue_index

    def get_execution_status(self) -> ExecutionStatus:
        """获取整体执行状态"""
        return self.execution_status

    def get_queue_statuses(self) -> List[QueueStatus]:
        """获取所有队列的状态（优化版本，确保非阻塞）"""
        try:
            return self.queue_statuses.copy()
        except Exception:
            # 如果出现异常，返回空列表
            return []

    def get_execution_errors(self) -> List[Dict[str, Any]]:
        """获取执行过程中的错误信息（优化版本，确保非阻塞）"""
        try:
            return self.execution_errors.copy()
        except Exception:
            # 如果出现异常，返回空列表
            return []

    def get_last_error(self) -> Optional[Dict[str, Any]]:
        """获取最后一个错误（优化版本，确保非阻塞）"""
        try:
            return self.last_error
        except Exception:
            # 如果出现异常，返回None
            return None

    def is_completed(self) -> bool:
        """判断所有队列是否已执行完成"""
        return self.execution_status == ExecutionStatus.COMPLETED

    def is_failed(self) -> bool:
        """判断执行是否失败"""
        return self.execution_status == ExecutionStatus.FAILED

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要信息（优化版本，确保非阻塞）"""
        try:
            # 快速获取基本计数信息
            total_queues = len(self.queues)
            completed_queues = sum(1 for status in self.queue_statuses if status == QueueStatus.COMPLETED)
            failed_queues = sum(1 for status in self.queue_statuses if status == QueueStatus.FAILED)

            # 快速计算持续时间
            duration = None
            if self.execution_start_time:
                end_time = self.execution_end_time or datetime.datetime.now()
                duration = int((end_time - self.execution_start_time).total_seconds())

            # 获取完成状态标志
            is_completed = (
                self.execution_status in [ExecutionStatus.COMPLETED, ExecutionStatus.FAILED, ExecutionStatus.STOPPED] or
                (not self.is_running and total_queues > 0 and completed_queues + failed_queues >= total_queues)
            )

            return {
                "execution_status": self.execution_status.value,
                "total_queues": total_queues,
                "completed_queues": completed_queues,
                "failed_queues": failed_queues,
                "current_queue_index": self.current_queue_index,
                "is_running": self.is_running,
                "is_completed": is_completed,
                "start_time": self.execution_start_time.isoformat() if self.execution_start_time else None,
                "end_time": self.execution_end_time.isoformat() if self.execution_end_time else None,
                "duration_seconds": duration,
                "error_count": len(self.execution_errors),
                "last_error": self.last_error
            }
        except Exception as e:
            # 如果出现任何异常，返回安全的默认值
            return {
                "execution_status": "error",
                "total_queues": 0,
                "completed_queues": 0,
                "failed_queues": 0,
                "current_queue_index": -1,
                "is_running": False,
                "is_completed": False,
                "start_time": None,
                "end_time": None,
                "duration_seconds": None,
                "error_count": 1,
                "last_error": {"error_message": f"获取状态异常: {str(e)}"}
            }

    def _record_error(self, queue_index: int, queue_title: str, error: Exception):
        """记录执行错误"""
        error_info = {
            "timestamp": datetime.datetime.now().isoformat(),
            "queue_index": queue_index,
            "queue_title": queue_title,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": str(error) if hasattr(error, '__traceback__') else None
        }

        self.execution_errors.append(error_info)
        self.last_error = error_info

        if self.logs:
            self.logs.error(f"队列执行错误: {error_info}")

    def get_new_file_name(self, device_id, formatted_datetime, list_len=1):
        """ 获取新的文件名称 """
        if formatted_datetime is None:
            formatted_datetime = self.get_current_time()

        # 获取设备列表
        device_list = self.adb.get_device_list()

        # 如果没有设备或device_id为None，则使用默认文件名
        if not device_list or device_id is None:
            return str(formatted_datetime) + "_" + str(list_len) + ".xlsx"

        # 如果只有一个设备，直接使用该设备名称
        if len(device_list) == 1:
            device_name = self.adb.get_device_name(device_list[0]).strip()
            return str(device_name) + "_" + str(formatted_datetime) + "_" + str(list_len) + ".xlsx"

        # 如果有多个设备，使用第一个设备名称，并在文件名中标注多设备
        device_name = self.adb.get_device_name(device_list[0]).strip()
        return str(device_name) + "_Multi_" + str(formatted_datetime) + "_" + str(list_len) + ".xlsx"

    def get_current_time(self):
        # 获取当前时间
        now = datetime.datetime.now()
        formatted_datetime = now.strftime('%Y-%m-%d %H-%M-%S')
        return formatted_datetime