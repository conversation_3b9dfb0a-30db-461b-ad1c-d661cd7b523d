import json
import os


# 定义默认预设配置
DEFAULT_PRESETS = {
    "5.0": {
        "内_OLED_自动": {
            "name": "内销OLED自动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "抖音",
                "快手",
                "腾讯视频",
                "爱奇艺",
                "QQ",
                "微博",
                "酷狗音乐",
                "微信",
                "高德地图"
            ]
        },
        "内_OLED_手动": {
            "name": "内销OLED手动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "负一屏",
                "系统电话",
                "系统短信",
                "应用商店",
                "vivo应用商店",
                "抖音",
                "快手",
                "微信视频",
                "王者荣耀",
                "和平精英",
                "原神",
                "星穹铁道",
                "金铲铲之战",
                "英雄联盟手游",
                "腾讯视频",
                "爱奇艺",
                "QQ",
                "微博",
                "酷狗音乐",
                "微信",
                "高德地图"
            ]
        },
        "外_OLED_自动": {
            "name": "外销OLED自动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "谷歌地图",
                "Snapchat",
                "亚马逊购物",
                "MyJio",
                "Hotstar",
                "Where is my Train",
                "Netflix"
            ]
        },
        "外_OLED_手动": {
            "name": "外销OLED手动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "负一屏",
                "外销电话",
                "外销短信",
                "外销vivo应用商店",
                "外销vivo官方商店",
                "和平精英",
                "MLBB",
                "FreeFire",
                "BGMI",
                "WhatsApp",
                "X",
                "Instagram",
                "Facebook",
                "TikTok（抖音国际版）",
                "YouTube",
                "谷歌地图",
                "Snapchat",
                "亚马逊购物",
                "MyJio",
                "Hotstar",
                "Where is my Train",
                "Netflix"
            ]
        },
        "内_LCD_自动": {
            "name": "内销LCD自动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "抖音",
                "快手",
                "腾讯视频",
                "爱奇艺",
                "QQ",
                "微博",
                "酷狗音乐",
                "微信",
                "高德地图"
            ]
        },
        "内_LCD_手动": {
            "name": "内销LCD手动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "抖音",
                "快手",
                "腾讯视频",
                "爱奇艺",
                "QQ",
                "微博",
                "酷狗音乐",
                "微信",
                "高德地图"
            ]
        },
        "外_LCD_自动": {
            "name": "外销LCD自动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "和平精英",
                "MLBB",
                "FreeFire",
                "BGMI",
                "WhatsApp",
                "X",
                "Instagram",
                "Facebook",
                "TikTok（抖音国际版）",
                "YouTube"
            ]
        },
        "外_LCD_手动": {
            "name": "外销LCD手动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "和平精英",
                "MLBB",
                "FreeFire",
                "BGMI",
                "WhatsApp",
                "X",
                "Instagram",
                "Facebook",
                "TikTok（抖音国际版）",
                "YouTube"
            ]
        }
    },
    "4.0": {
        "auto_manual": {
            "name": "自/手动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "抖音",
                "快手",
                "腾讯视频",
                "爱奇艺",
                "QQ",
                "微博",
                "酷狗音乐",
                "微信",
                "高德地图"
            ]
        },
        "ex_auto_manual": {
            "name": "外销自/手动亮度",
            "apps": [
                "系统桌面",
                "系统相册",
                "TikTok",
                "YouTube",
                "QQ",
                "Facebook",
                "微信",
                "Google Maps"
            ]
        },
        "manual": {
            "name": "手动",
            "apps": [
                "系统桌面",
                "负一屏",
                "系统相册",
                "系统电话",
                "外销电话",
                "系统短信"
            ]
        },
        "ex_manual": {
            "name": "外销手动",
            "apps": [
                "系统桌面",
                "负一屏",
                "vivo应用商店",
                "酷狗音乐",
                "抖音",
                "快手",
                "腾讯视频"
            ]
        }
    }
}


class PresetsConfig:
    """预设配置管理类，用于从JSON文件中读取预设配置信息"""
    def __init__(self, config_path="presets_config.json", version="5.0"):
        """
        初始化预设配置管理类
        :param config_path: 配置文件路径
        :param version: 使用的标准版本 4.0 或 5.0
        """
        self.config_path = config_path
        self.version = version
        self.presets_config = self._load_config()
    
    def _load_config(self):
        """加载JSON配置文件"""
        if not os.path.exists(self.config_path):
            # 如果配置文件不存在，尝试在_internal目录下查找
            internal_path = os.path.join("_internal", self.config_path)
            if os.path.exists(internal_path):
                self.config_path = internal_path
            else:
                # 如果配置文件不存在，使用默认预设创建它
                try:
                    with open(self.config_path, 'w', encoding='utf-8') as json_file:
                        json.dump(DEFAULT_PRESETS, json_file, ensure_ascii=False, indent=4)
                    return DEFAULT_PRESETS.copy()
                except Exception as e:
                    print(f"创建默认预设配置文件失败: {e}")
                    return {}
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as json_file:
                return json.load(json_file)
        except Exception as e:
            print(f"加载预设配置文件失败: {e}")
            return {}
    
    def set_version(self, version):
        """设置使用的标准版本"""
        self.version = version
    
    def get_preset_apps(self, preset_key):
        """
        获取预设模式的应用列表
        :param preset_key: 预设模式的键名
        :return: 应用列表
        """
        # 获取当前版本的预设配置
        version_config = self.presets_config.get(self.version, {})
        # 获取指定预设模式的配置
        preset_config = version_config.get(preset_key, {})
        # 返回应用列表
        return preset_config.get("apps", [])
    
    def get_preset_name(self, preset_key):
        """
        获取预设模式的名称
        :param preset_key: 预设模式的键名
        :return: 预设模式的名称
        """
        # 获取当前版本的预设配置
        version_config = self.presets_config.get(self.version, {})
        # 获取指定预设模式的配置
        preset_config = version_config.get(preset_key, {})
        # 返回预设模式的名称
        return preset_config.get("name", "")
    
    def get_all_presets(self):
        """
        获取当前版本的所有预设模式
        :return: 预设模式字典 {preset_key: preset_name}
        """
        # 获取当前版本的预设配置
        version_config = self.presets_config.get(self.version, {})
        # 返回预设模式字典
        return {preset_key: preset_config.get("name", "") for preset_key, preset_config in version_config.items()}
    
    def save_preset_apps(self, preset_key, apps):
        """
        保存预设模式的应用列表
        :param preset_key: 预设模式的键名
        :param apps: 应用列表
        :return: 是否保存成功
        """
        # 获取当前版本的预设配置
        version_config = self.presets_config.get(self.version, {})
        # 获取指定预设模式的配置
        preset_config = version_config.get(preset_key, {})
        # 如果预设模式不存在，创建一个新的
        if not preset_config:
            # Map preset_key to a human-readable name
            if self.version == "5.0":
                name_map = {
                    "内_OLED_自动": "内销OLED自动亮度",
                    "内_OLED_手动": "内销OLED手动亮度",
                    "外_OLED_自动": "外销OLED自动亮度",
                    "外_OLED_手动": "外销OLED手动亮度",
                    "内_LCD_自动": "内销LCD自动亮度",
                    "内_LCD_手动": "内销LCD手动亮度",
                    "外_LCD_自动": "外销LCD自动亮度",
                    "外_LCD_手动": "外销LCD手动亮度"
                }
            else:  # 4.0版本使用旧的名称映射
                name_map = {
                    "auto_manual": "自/手动亮度",
                    "ex_auto_manual": "外销自/手动亮度",
                    "manual": "手动",
                    "ex_manual": "外销手动"
                }
            preset_name = name_map.get(preset_key, preset_key)
            preset_config = {"name": preset_name, "apps": []}
            version_config[preset_key] = preset_config
        
        # 更新应用列表
        preset_config["apps"] = apps
        
        # 保存配置
        return self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as json_file:
                json.dump(self.presets_config, json_file, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存预设配置文件失败: {e}")
            return False
            
    def restore_default_preset(self, preset_key=None):
        """
        恢复默认预设
        :param preset_key: 预设模式的键名
                          如果为None，则恢复当前版本的所有预设
        :return: 是否恢复成功
        """
        # 获取当前版本的默认预设
        default_version_config = DEFAULT_PRESETS.get(self.version, {})
        
        # 如果当前版本没有默认预设，则返回失败
        if not default_version_config:
            return False
        
        # 如果没有指定预设键名，则恢复当前版本的所有预设
        if preset_key is None:
            # 获取或创建当前版本的配置
            if self.version not in self.presets_config:
                self.presets_config[self.version] = {}
                
            # 恢复当前版本的所有默认预设
            for key, value in default_version_config.items():
                self.presets_config[self.version][key] = value.copy()
        else:
            # 获取指定预设的默认配置
            default_preset = default_version_config.get(preset_key)
            
            # 如果指定的预设不存在于默认配置中，则返回失败
            if not default_preset:
                return False
            
            # 获取或创建当前版本的配置
            if self.version not in self.presets_config:
                self.presets_config[self.version] = {}
                
            # 恢复指定的默认预设
            self.presets_config[self.version][preset_key] = default_preset.copy()
        
        # 保存配置
        return self.save_config()
        
    def restore_all_default_presets(self):
        """
        恢复所有版本的所有默认预设
        :return: 是否恢复成功
        """
        # 恢复所有默认预设
        self.presets_config = DEFAULT_PRESETS.copy()
        
        # 保存配置
        return self.save_config()
    
    def reload(self):
        """重新从文件加载预设配置"""
        self.presets_config = self._load_config()
        return True 