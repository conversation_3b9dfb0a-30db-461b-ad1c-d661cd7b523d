from qgui.base_tools import BaseTool


class GlobalInstanceBase:
    """
    全局组件实例基础类
    用于处理在执行过程中，需要在多个组件之间传递数据的情况
    如更改组件的状态，样式，数据等
    """
    def __init__(self):
        self._instance_dict = {}

    def add_instance(self, instance: BaseTool):
        """ 添加实例 """
        self._instance_dict[instance.name] = instance

    def get_instance(self, name: str):
        """ 获取实例 """
        return self._instance_dict.get(name)
