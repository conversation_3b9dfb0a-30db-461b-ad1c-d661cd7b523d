import json
import os


def check_file_exists(file_path):
    """ 判断文件是否存在 """
    return os.path.exists(file_path)

def get_4_config():
    # 打开并读取JSON文件
    config_path = 'config_4.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_4.json'
    with open(config_path, 'r', encoding='utf-8') as json_file:
        config_data = json.load(json_file)

    return config_data


def get_5_config():
    config_path = 'config_5.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_5.json'
    # 打开并读取JSON文件
    with open(config_path, 'r', encoding='utf-8') as json_file:
        config_data = json.load(json_file)

    return config_data


def update_4_config(key, new_value):
    config_path = 'config_4.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_4.json'
    # 读取当前配置
    config_data = get_4_config()

    # 更新配置数据
    if key in config_data:
        config_data[key] = new_value
        # 将更新后的配置写回JSON文件
        with open(config_path, 'w', encoding='utf-8') as json_file:
            json.dump(config_data, json_file, ensure_ascii=False, indent=4)
        # print(f"Updated {key} successfully.")
    else:
        print(f"Key '{key}' not found in configuration.")


def update_5_config(key, new_value):
    config_path = 'config_5.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_5.json'
    # 读取当前配置
    config_data = get_5_config()

    # 更新配置数据
    if key in config_data:
        config_data[key] = new_value
        # 将更新后的配置写回JSON文件
        with open(config_path, 'w', encoding='utf-8') as json_file:
            json.dump(config_data, json_file, ensure_ascii=False, indent=4)
        # print(f"Updated {key} successfully.")
    else:
        print(f"Key '{key}' not found in configuration.")