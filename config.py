import json
import os


def check_file_exists(file_path):
    """ 判断文件是否存在 """
    return os.path.exists(file_path)

def get_4_config():
    # 打开并读取JSON文件
    config_path = 'config_4.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_4.json'
    with open(config_path, 'r', encoding='utf-8') as json_file:
        config_data = json.load(json_file)

    return config_data


def get_5_config():
    config_path = 'config_5.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_5.json'
    # 打开并读取JSON文件
    with open(config_path, 'r', encoding='utf-8') as json_file:
        config_data = json.load(json_file)

    return config_data


def update_4_config(key, new_value):
    config_path = 'config_4.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_4.json'
    # 读取当前配置
    config_data = get_4_config()

    # 更新配置数据
    if key in config_data:
        config_data[key] = new_value
        # 将更新后的配置写回JSON文件
        with open(config_path, 'w', encoding='utf-8') as json_file:
            json.dump(config_data, json_file, ensure_ascii=False, indent=4)
        # print(f"Updated {key} successfully.")
    else:
        print(f"Key '{key}' not found in configuration.")


def update_5_config(key, new_value):
    config_path = 'config_5.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_5.json'
    # 读取当前配置
    config_data = get_5_config()

    # 更新配置数据
    if key in config_data:
        config_data[key] = new_value
        # 将更新后的配置写回JSON文件
        with open(config_path, 'w', encoding='utf-8') as json_file:
            json.dump(config_data, json_file, ensure_ascii=False, indent=4)
        # print(f"Updated {key} successfully.")
    else:
        print(f"Key '{key}' not found in configuration.")


def get_app_startup_delays_5():
    """获取5.0版本的应用启动等待时间配置"""
    config_data = get_5_config()
    delays = config_data.get("APP_STARTUP_DELAYS", {})
    return {
        "init_delay": delays.get("init_delay", 10),
        "brightness_stabilize_delay": delays.get("brightness_stabilize_delay", 10)
    }


def get_app_startup_delays_4():
    """获取4.0版本的应用启动等待时间配置"""
    config_data = get_4_config()
    delays = config_data.get("APP_STARTUP_DELAYS", {})
    return {
        "init_delay": delays.get("init_delay", 10),
        "brightness_stabilize_delay": delays.get("brightness_stabilize_delay", 10)
    }


def update_app_startup_delays_5(init_delay=None, brightness_stabilize_delay=None):
    """更新5.0版本的应用启动等待时间配置"""
    config_path = 'config_5.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_5.json'

    config_data = get_5_config()

    if "APP_STARTUP_DELAYS" not in config_data:
        config_data["APP_STARTUP_DELAYS"] = {
            "desc": "应用启动等待时间配置",
            "init_delay": 10,
            "brightness_stabilize_delay": 10,
            "comment": "延时时间单位为秒，init_delay为初始化后等待时间，brightness_stabilize_delay为亮度设置后等待时间"
        }

    if init_delay is not None:
        config_data["APP_STARTUP_DELAYS"]["init_delay"] = init_delay
    if brightness_stabilize_delay is not None:
        config_data["APP_STARTUP_DELAYS"]["brightness_stabilize_delay"] = brightness_stabilize_delay

    # 将更新后的配置写回JSON文件
    with open(config_path, 'w', encoding='utf-8') as json_file:
        json.dump(config_data, json_file, ensure_ascii=False, indent=4)


def update_app_startup_delays_4(init_delay=None, brightness_stabilize_delay=None):
    """更新4.0版本的应用启动等待时间配置"""
    config_path = 'config_4.json'
    if not check_file_exists(config_path):
        config_path = '_internal/config_4.json'

    config_data = get_4_config()

    if "APP_STARTUP_DELAYS" not in config_data:
        config_data["APP_STARTUP_DELAYS"] = {
            "desc": "应用启动等待时间配置",
            "init_delay": 10,
            "brightness_stabilize_delay": 10,
            "comment": "延时时间单位为秒，init_delay为初始化后等待时间，brightness_stabilize_delay为亮度设置后等待时间"
        }

    if init_delay is not None:
        config_data["APP_STARTUP_DELAYS"]["init_delay"] = init_delay
    if brightness_stabilize_delay is not None:
        config_data["APP_STARTUP_DELAYS"]["brightness_stabilize_delay"] = brightness_stabilize_delay

    # 将更新后的配置写回JSON文件
    with open(config_path, 'w', encoding='utf-8') as json_file:
        json.dump(config_data, json_file, ensure_ascii=False, indent=4)