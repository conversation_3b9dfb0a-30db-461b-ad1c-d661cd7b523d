from enum import Enum, auto
import config


class PackageName(Enum):
    def __new__(cls, key):
        # 从配置文件中获取值
        value = config.get_5_config().get(key)
        obj = object.__new__(cls)
        obj._value_ = value
        return obj

    # 枚举成员使用配置文件中的键名
    DESKTOP = 'DESKTOP'
    NEGATIVE_ONE_SCREEN = 'NEGATIVE_ONE_SCREEN'
    SYSPHOTO = 'SYSPHOTO'
    SYSPHONE = 'SYSPHONE'
    SYSEXPHONE = 'SYSEXPHONE'
    SYSMMS = 'SYSMMS'
    SYSEXMMS = 'SYSEXMMS'
    APPLICATIONSTORE = 'APPLICATIONSTORE'
    VIVOSTORE = 'VIVOSTORE'
    DOUYIN = 'DOUYIN'
    KUAISHOU = 'KUAISHOU'
    TENGXUNSHIPIN = 'TENGXUNSHIPIN'
    AIQIYI = 'AIQIYI'
    QQ = 'QQ'
    WEIBO = 'WEIBO'
    EXFACEBOOK = 'EXFACEBOOK'
    KUGOU = 'KUGOU'
    WEIXIN = 'WEIXIN'
    WEIXIN_VIDEO = 'WEIXIN_VIDEO'
    YUANSHEN = 'YUANSHEN'
    XINGQIONGTIEDAO = 'XINGQIONGTIEDAO'
    WANGZHERONGYAO = 'WANGZHERONGYAO'
    HEPINGJINGYING = 'HEPINGJINGYING'
    PUBG = 'PUBG'
    EX_YUANSHEN = 'EX_YUANSHEN'
    JINCHANCHAN = 'JINCHANCHAN'
    EX_TFT = 'EX_TFT'
    EX_XINGQIONGTIEDAO = 'EX_XINGQIONGTIEDAO'
    LOLM = 'LOLM'
    EX_LOLM = 'EX_LOLM'
    EX_CHUANSHUODUIJUE = 'EX_CHUANSHUODUIJUE'
    EX_WANGZHERONGYAO = 'EX_WANGZHERONGYAO'
    TIKTOK = 'TIKTOK'
    YOUTUBE = 'YOUTUBE'
    GOOGLE_MAPS = 'GOOGLE_MAPS'
    EX_APPLICATION_STORE = 'EX_APPLICATION_STORE'
    EX_VIVO_STORE = 'EX_VIVO_STORE'
    GAODE_MAPS = 'GAODE_MAPS'

    @classmethod
    def get_tuples(cls):
        res = []
        for item in cls:
            res.append((item.value.get("desc"), 0))
        return res

    @classmethod
    def get_package_auto_list(self):
        """ 自动亮度 """
        res = [
            PackageName.DESKTOP,
            PackageName.SYSPHOTO,
            PackageName.QQ,
            PackageName.WEIBO,
            PackageName.KUGOU,
            PackageName.WEIXIN,
            PackageName.GAODE_MAPS,
            PackageName.NEGATIVE_ONE_SCREEN,
            PackageName.SYSPHONE,
            PackageName.SYSMMS,
            PackageName.APPLICATIONSTORE,
            PackageName.VIVOSTORE,
        ]
        return res

    @classmethod
    def get_package_manual_list(self):
        """ 手动亮度 """
        res = [
            PackageName.DESKTOP,
            PackageName.DOUYIN,
            PackageName.KUAISHOU,
            PackageName.TENGXUNSHIPIN,
            PackageName.AIQIYI,
            PackageName.WEIXIN_VIDEO,
            PackageName.WANGZHERONGYAO,
            PackageName.HEPINGJINGYING,
            PackageName.YUANSHEN,
            PackageName.XINGQIONGTIEDAO,
            PackageName.JINCHANCHAN,
            PackageName.LOLM,
        ]
        return res

    @classmethod
    def get_package_ex_auto_list(self):
        """ 外销自动亮度 """
        res = [
            PackageName.DESKTOP,
            PackageName.SYSPHOTO,
            PackageName.TIKTOK,
            PackageName.YOUTUBE,
            PackageName.QQ,
            PackageName.EXFACEBOOK,
            PackageName.WEIXIN,
            PackageName.GOOGLE_MAPS,
            PackageName.NEGATIVE_ONE_SCREEN,
            PackageName.SYSEXPHONE,
            PackageName.SYSMMS,
            PackageName.EX_APPLICATION_STORE,
            PackageName.EX_VIVO_STORE,
        ]
        return res

    @classmethod
    def get_package_ex_manual_list(self):
        """ 外销手动亮度 """
        res = [
            PackageName.DESKTOP,
            PackageName.WEIXIN_VIDEO,
            PackageName.EX_WANGZHERONGYAO,
            PackageName.PUBG,
            PackageName.EX_YUANSHEN,
            PackageName.EX_XINGQIONGTIEDAO,
            PackageName.EX_TFT,
            PackageName.EX_LOLM,
            PackageName.YOUTUBE,
        ]
        return res

    @classmethod
    def get_package_outdoor_auto_and_manual_list(self):
        """ 室外自动+手动亮度 """
        res = [
            PackageName.DOUYIN,
            PackageName.KUAISHOU,
            PackageName.WEIXIN_VIDEO,
            PackageName.WANGZHERONGYAO,
            PackageName.HEPINGJINGYING,
            PackageName.YUANSHEN,
            PackageName.XINGQIONGTIEDAO,
            PackageName.JINCHANCHAN,
            PackageName.LOLM,
        ]
        return res

    @classmethod
    def get_package_auto_and_manual_list_4_0(self):
        """ 室内自动/手动亮度4.0 """
        res = [
            PackageName.DESKTOP,
            PackageName.SYSPHOTO,
            PackageName.DOUYIN,
            PackageName.KUAISHOU,
            PackageName.TENGXUNSHIPIN,
            PackageName.AIQIYI,
            PackageName.QQ,
            PackageName.WEIBO,
            PackageName.KUGOU,
            PackageName.WEIXIN,
            PackageName.GAODE_MAPS
        ]
        return res