# 省电机制组-显示亮度降幅自动化程序

## 应用配置系统

本项目现在支持通过JSON配置文件来管理测试应用，无需修改代码即可添加新的测试应用。

### 生成配置文件

首次使用配置系统时，需要从现有的PackageName枚举生成配置文件：

```bash
python utils/GenerateAppConfig.py
```

这将在项目根目录下生成`app_config.json`文件。

### 配置文件结构

配置文件的结构如下：

```json
{
    "应用键名": {
        "desc": "应用描述",
        "package": "应用包名",
        "activity": "应用Activity",
        "app_name": "应用实际名称",
        "open_method": "打开方式",
        "standards": {
            "5.0": {
                "indoor": "室内标准值",
                "outdoor": "室外标准值"
            },
            "4.0": {
                "indoor": "室内标准值",
                "outdoor": "室外标准值"
            }
        },
        "type": ["应用类型"]
    }
}
```

各字段说明：

- `desc`: 应用描述，用于显示和识别
- `package`: 应用包名，用于ADB打开应用
- `activity`: 应用Activity，当ADB命令无法打开时使用
- `app_name`: 应用实际名称，用于uiautomator2搜索
- `open_method`: 打开方式，可选值：
  - `package`: 使用包名打开（默认）
  - `activity`: 使用Activity打开
  - `ui`: 使用uiautomator2查找并点击应用
- `standards`: 标准值配置
  - `5.0`: 5.0标准
  - `4.0`: 4.0标准
  - 每个标准包含`indoor`和`outdoor`两个值，分别表示室内和室外标准
  - 每个值的格式为`自动亮度标准/手动亮度标准`
- `type`: 应用类型，可包含以下值：
  - `auto`: 自动亮度测试
  - `manual`: 手动亮度测试
  - `ex_auto`: 外销自动亮度测试
  - `ex_manual`: 外销手动亮度测试
  - `outdoor`: 户外亮度测试

### 添加新应用

要添加新应用，只需在`app_config.json`文件中添加新的配置项即可。例如：

```json
"NEW_APP": {
    "desc": "新应用",
    "package": "com.example.newapp",
    "activity": "com.example.newapp.MainActivity",
    "app_name": "新应用",
    "open_method": "activity",
    "standards": {
        "5.0": {
            "indoor": "5%/400nit",
            "outdoor": "0/0"
        },
        "4.0": {
            "indoor": "5%/400nit",
            "outdoor": "0/0"
        }
    },
    "type": ["auto", "manual"]
}
```

添加完成后，系统会自动识别并使用新的应用配置。

### 配置文件示例

项目中提供了一个配置文件示例`app_config_example.json`，可以参考该文件来添加新的应用。

### python环境
python：3.10.11

### 打包软件
PyInstaller: 6.11.1
打包命令：
pyinstaller main.spec

### 当前迭代版本：v_2.1.0
### 当前迭代版本：v_2.1.1
1.优化保存逻辑，测试人等信息只输出一次
### 当前迭代版本：v_2.1.2
1.修复文件保存出错的bug
### 当前迭代版本：v_2.1.3
1.实现预设恢复功能，用户可在预设配置页面一键恢复默认预设
2.解决预设恢复后需要重启程序才能生效的问题
3.优化预设配置界面，使保存与恢复按钮布局更合理
4.扩展预设配置，通过配置新增app逻辑。
### 当前迭代版本：v_3.0.1
1.新增服务器功能，可提供API让第三方应用调用（如vcts）
2.可靠性，稳定性提升

### 当前功能：
1. 显示亮度降幅4.0
2. 显示亮度降幅5.0

## 注意事项
1.出现无法获取亮度的情况，重启一下手机，亮度日志的记录是会累加的，日志太长时，程序读取需要的时间也会增加。
