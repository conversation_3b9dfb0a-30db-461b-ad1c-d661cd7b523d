import logging
import logging.config
import os
import glob
import time
from datetime import datetime, timedelta
import sys

# 日志格式
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(name)s - %(message)s'

# 日志文件基础名
APP_LOG_BASE = 'app'
CONSOLE_LOG_BASE = 'console'

# 是否已经初始化标志，防止重复初始化
_initialized = False


def get_daily_log_filename(base_name: str, log_dir: str = 'logs') -> str:
    """
    生成带日期的日志文件名

    Args:
        base_name: 日志文件基础名（如'app', 'console'）
        log_dir: 日志目录路径

    Returns:
        完整的日志文件路径，格式为: logs/app_2025-07-06.log
    """
    today = datetime.now().strftime('%Y-%m-%d')
    filename = f"{base_name}_{today}.log"
    return os.path.join(log_dir, filename)

# 添加流转日志工具类
class _StreamToLogger:
    """将 `sys.stdout` / `sys.stderr` 重定向到 logging。"""
    def __init__(self, logger: logging.Logger, level: int):
        self.logger = logger
        self.level = level

    def write(self, buf):
        for line in buf.rstrip().splitlines():
            self.logger.log(self.level, line.rstrip())

    def flush(self):
        pass


def cleanup_old_logs(log_dir: str = 'logs', retention_days: int = 30):
    """
    清理超过保留天数的日志文件

    Args:
        log_dir: 日志目录路径
        retention_days: 日志保留天数
    """
    if not os.path.exists(log_dir):
        return

    cutoff_date = datetime.now() - timedelta(days=retention_days)
    deleted_count = 0

    # 查找所有日志文件（包括新旧格式）
    log_patterns = [
        os.path.join(log_dir, '*.log.*'),  # 旧格式: app.log.2025-06-20, api.log.2025-06-20
        os.path.join(log_dir, '*_????-??-??.log'),  # 新格式: app_2025-07-06.log, console_2025-07-06.log
    ]

    for pattern in log_patterns:
        for log_file in glob.glob(pattern):
            try:
                filename = os.path.basename(log_file)
                file_date = None

                # 尝试从文件名中提取日期（新格式）
                if '_' in filename and filename.endswith('.log'):
                    try:
                        date_part = filename.split('_')[1].replace('.log', '')
                        file_date = datetime.strptime(date_part, '%Y-%m-%d')
                    except (ValueError, IndexError):
                        pass

                # 如果无法从文件名提取日期，使用文件修改时间（旧格式）
                if file_date is None:
                    file_mtime = os.path.getmtime(log_file)
                    file_date = datetime.fromtimestamp(file_mtime)

                # 如果文件超过保留天数，则删除
                if file_date < cutoff_date:
                    os.remove(log_file)
                    deleted_count += 1
                    print(f"已删除过期日志文件: {filename}")

            except Exception as e:
                print(f"删除日志文件失败 {log_file}: {e}")

    if deleted_count > 0:
        print(f"日志清理完成，共删除 {deleted_count} 个过期文件")


def init_logging(log_dir: str = 'logs', retention_days: int = 30):
    """
    初始化统一日志配置。使用带日期的文件名，适合间歇运行的程序。

    Args:
        log_dir: 日志目录路径
        retention_days: 日志保留天数，默认30天
    """
    global _initialized
    if _initialized:
        return

    # 确保日志目录存在
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    # 执行启动时的日志清理
    cleanup_old_logs(log_dir, retention_days)

    # 生成当天的日志文件名
    app_log_file = get_daily_log_filename(APP_LOG_BASE, log_dir)
    console_log_file = get_daily_log_filename(CONSOLE_LOG_BASE, log_dir)

    config_dict = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': LOG_FORMAT
            }
        },
        'handlers': {
            # 主应用日志，使用当天日期的文件名
            'app_file': {
                'class': 'logging.FileHandler',
                'level': 'INFO',
                'formatter': 'standard',
                'filename': app_log_file,
                'encoding': 'utf-8',
                'mode': 'a',  # 追加模式，如果文件存在则追加
            },
            # 控制台日志文件，使用当天日期的文件名
            'console_file': {
                'class': 'logging.FileHandler',
                'level': 'DEBUG',
                'formatter': 'standard',
                'filename': console_log_file,
                'encoding': 'utf-8',
                'mode': 'a',  # 追加模式，如果文件存在则追加
            },
        },
        'root': {
            'handlers': ['app_file', 'console_file'],
            'level': 'INFO',
        }
    }

    logging.config.dictConfig(config_dict)
    # 重定向标准输出/错误到日志
    sys.stdout = _StreamToLogger(logging.getLogger('STDOUT'), logging.INFO)
    sys.stderr = _StreamToLogger(logging.getLogger('STDERR'), logging.ERROR)
    _initialized = True

    # 记录初始化信息
    logger = logging.getLogger('LoggerManager')
    logger.info(f"日志系统初始化完成 - 保留天数: {retention_days}天, 日志目录: {log_dir}")
    logger.info(f"当天日志文件: {os.path.basename(app_log_file)}, {os.path.basename(console_log_file)}")


def get_logger(name: str = None) -> logging.Logger:
    """获取 logger，对外暴露统一接口。"""
    if not _initialized:
        init_logging()
    return logging.getLogger(name)


def get_log_statistics(log_dir: str = 'logs') -> dict:
    """
    获取日志系统统计信息

    Args:
        log_dir: 日志目录路径

    Returns:
        包含日志统计信息的字典
    """
    if not os.path.exists(log_dir):
        return {'error': '日志目录不存在'}

    try:
        log_files = []
        total_size = 0

        # 查找所有日志文件（新旧格式）
        for pattern in ['*.log', '*.log.*', '*_????-??-??.log']:
            for log_file in glob.glob(os.path.join(log_dir, pattern)):
                if os.path.isfile(log_file):
                    log_files.append(log_file)
                    total_size += os.path.getsize(log_file)

        # 获取当天的日志文件
        current_app_log = get_daily_log_filename(APP_LOG_BASE, log_dir)
        current_console_log = get_daily_log_filename(CONSOLE_LOG_BASE, log_dir)

        return {
            'total_files': len(log_files),
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'log_directory': log_dir,
            'current_app_log': current_app_log,
            'current_console_log': current_console_log
        }
    except Exception as e:
        return {'error': f'获取统计信息失败: {e}'}


def manual_log_cleanup(log_dir: str = 'logs', retention_days: int = 30, dry_run: bool = False) -> tuple[int, int]:
    """
    手动执行日志清理

    Args:
        log_dir: 日志目录路径
        retention_days: 日志保留天数
        dry_run: 是否为试运行模式，不实际删除文件

    Returns:
        (成功删除的文件数, 失败的文件数)
    """
    if not os.path.exists(log_dir):
        return 0, 0

    cutoff_date = datetime.now() - timedelta(days=retention_days)
    success_count = 0
    failed_count = 0

    # 查找所有日志文件（新旧格式）
    log_patterns = [
        os.path.join(log_dir, '*.log.*'),  # 旧格式: app.log.2025-06-20, api.log.2025-06-20
        os.path.join(log_dir, '*_????-??-??.log'),  # 新格式: app_2025-07-06.log, console_2025-07-06.log
    ]

    for pattern in log_patterns:
        for log_file in glob.glob(pattern):
            try:
                filename = os.path.basename(log_file)
                file_date = None

                # 尝试从文件名中提取日期（新格式）
                if '_' in filename and filename.endswith('.log'):
                    try:
                        date_part = filename.split('_')[1].replace('.log', '')
                        file_date = datetime.strptime(date_part, '%Y-%m-%d')
                    except (ValueError, IndexError):
                        pass

                # 如果无法从文件名提取日期，使用文件修改时间（旧格式）
                if file_date is None:
                    file_mtime = os.path.getmtime(log_file)
                    file_date = datetime.fromtimestamp(file_mtime)

                # 如果文件超过保留天数
                if file_date < cutoff_date:
                    if dry_run:
                        print(f"[试运行] 将删除过期日志文件: {filename}")
                        success_count += 1
                    else:
                        os.remove(log_file)
                        print(f"已删除过期日志文件: {filename}")
                        success_count += 1

            except Exception as e:
                print(f"删除日志文件失败 {log_file}: {e}")
                failed_count += 1

    return success_count, failed_count