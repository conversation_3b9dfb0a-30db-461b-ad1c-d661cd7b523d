#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成应用配置文件
该脚本将从现有的PackageName枚举和配置文件中生成app_config.json文件
"""

import os
import sys
import json

# 添加项目根目录到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.ConfigConverter import ConfigConverter


def main():
    """主函数"""
    print("开始生成应用配置文件...")
    
    # 检查是否已存在配置文件
    if os.path.exists("app_config.json"):
        choice = input("app_config.json文件已存在，是否覆盖？(y/n): ")
        if choice.lower() != 'y':
            print("已取消生成操作。")
            return
    
    # 生成配置文件
    success = ConfigConverter.convert_to_json()
    
    if success:
        print("应用配置文件生成成功！")
        print("文件路径: app_config.json")
        
        # 验证配置文件
        valid = ConfigConverter.validate_config()
        if valid:
            print("配置文件验证通过。")
        else:
            print("配置文件验证失败，请检查配置文件。")
    else:
        print("应用配置文件生成失败。")


if __name__ == "__main__":
    main() 