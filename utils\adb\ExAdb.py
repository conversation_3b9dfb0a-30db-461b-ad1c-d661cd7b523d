""" ExAdb.py """
import random
import re
import subprocess
import time

from utils.adb.BaseAdb import BaseAdb


class ExAdb(BaseAdb):
    def __init__(self):
        super().__init__()

    def page_left_to(self, device_id=None):
        """向左滑动"""
        if device_id:
            self.user_id_run_adb(dev_id=device_id, str="input swipe 800 500 200 500")
            return
        self.user_run_adb("adb shell input swipe 800 500 200 500")

    def page_right_to(self, device_id=None):
        """向右滑动"""
        if device_id:
            self.user_id_run_adb(dev_id=device_id, str="input swipe 200 500 800 500")
            return
        self.user_run_adb("adb shell input swipe 200 500 800 500")

    def open_app(self):
        """启动app"""
        self.user_run_adb("adb shell am start -n com.vivo.greyscaletest/com.vivo.greyscaletest.SecondActivity")

    def stop_app(self):
        """关闭app"""
        self.user_run_adb("adb shell am force-stop com.vivo.greyscaletest")

    def get_activity_info(self, device_id=None):
        """ 获取当前屏幕中的应用信息 """
        if device_id:
            value = self.user_id_run_adb_stdout(device_id, '"dumpsys window | grep mCurrentFocus"')
        else:
            value = self.user_run_adb_stdout('adb shell "dumpsys window | grep mCurrentFocus"')

        match = re.search(r'mCurrentFocus=Window{\w+ u\d+ (\S+)/', value)
        if match:
            package_name = match.group(1)
            return package_name
        else:
            return None

    def get_software_version(self, device_id=None):
        """ 获取设备软件版本 """
        if device_id:
            return self.user_run_adb_stdout("adb -s " + device_id + " shell getprop ro.vivo.product.version")
        return self.user_run_adb_stdout("adb shell getprop ro.vivo.product.version")


    def stop_current_app(self, device_id=None):
        """ 关闭当前app """
        # 获取当前屏幕信息
        package_name = self.get_activity_info(device_id)
        if package_name:
            if device_id:
                subprocess.run("adb -s " + device_id + " shell am force-stop " + package_name, shell=True)
                return
            subprocess.run("adb shell am force-stop " + package_name, shell=True)

    def start_dark_mode(self):
        """开启深色模式"""
        self.user_run_adb_stdout("adb shell cmd uimode night yes")

    def stop_dark_mode(self):
        """关闭深色模式"""
        self.user_run_adb_stdout("adb shell cmd uimode night no")

    def is_dark_mode(self):
        """ 判断是否开启深色模式 """
        return self.user_run_adb_stdout("adb shell cmd uimode night")

    def upload_file(self, location="static_grey", remote="/sdcard/"):
        """ 上传文件到设备存储中 """
        # 检查设备中是否存在该文件
        adb_command = ["adb", "shell", "test", "-d", remote + location, "&&", "echo", "-n", "exists", "||", "echo",
                       "-n", "not exists"]
        result = self.user_run_adb_stdout(adb_command)
        if 'not exists' in result:
            # 上传文件
            self.user_run_adb("adb push " + location + " " + remote)# 延时5秒，等待文件上传完毕
            num = 0
            while True:
                # 随机数
                num += random.randint(10, 20)
                time.sleep(0.5)
                if num >= 100:
                    print("100%...")
                    print("上传文件完毕...")
                    break
                print(str(num) + "%...")
        return True

    def open_system_image(self, file_name, file_suffix="jpg"):
        """ 使用系统相册打开指定图片 """
        self.user_run_adb(
            'adb shell am start -a android.intent.action.VIEW -n com.vivo.gallery/com.android.gallery3d.app.Gallery -d "file:///sdcard/static_grey/' + file_name + '.' + file_suffix + '" -t "image/*"')

    def open_system_grey_image(self, index: int, file_suffix="jpg"):
        """
        使用系统相册打开指定灰阶图片
        MAX 64 白
        MIN 00 黑
        """
        # self.close_system_image()
        # 替换更通用的关闭方式
        self.stop_current_app()
        if index >= 10:
            file_name = 'P3_gray' + str(index)
        else:
            file_name = 'P3_gray0' + str(index)
        time.sleep(1)
        self.user_run_adb(
            'adb shell am start -a android.intent.action.VIEW -n com.vivo.gallery/com.android.gallery3d.app.Gallery -d "file:///sdcard/static_grey/' + file_name + '.' + file_suffix + '" -t "image/*"')
        time.sleep(1)
        self.click_double()
        pass

    def close_system_image(self, package_name="com.vivo.gallery"):
        """ 关闭系统相册 """
        self.user_run_adb("adb shell am force-stop " + package_name)

    def click_double(self, x=600, y=600, delay=0.01):
        """
        使用ADB命令模拟屏幕上的双击操作。

        参数:
        x, y - 要双击的屏幕坐标
        delay - 两次点击之间的延迟时间，单位为秒
        """
        # 构建单次点击的ADB命令
        tap_cmd = "adb shell input tap {x} {y}".format(x=x, y=y)

        # 执行第一次点击
        subprocess.run(tap_cmd, shell=True)
        # 等待指定的延迟时间
        time.sleep(delay)
        # 执行第二次点击
        subprocess.run(tap_cmd, shell=True)

    def click_one(self, x=600, y=600, delay=0.01):
        """
        使用ADB命令模拟屏幕上的双击操作。

        参数:
        x, y - 要双击的屏幕坐标
        delay - 两次点击之间的延迟时间，单位为秒
        """
        # 构建单次点击的ADB命令
        tap_cmd = "adb shell input tap {x} {y}".format(x=x, y=y)

        # 执行第一次点击
        subprocess.run(tap_cmd, shell=True)

    def click_screen(self, x, y):
        subprocess.run("adb shell input tap " + str(x) + " " + str(y), shell=True)

    def on_menu(self):
        """返回主菜单"""
        self.user_run_adb("adb shell input keyevent KEYCODE_MENU")

    def get_root(self):
        """获取root权限"""
        # 检查是否存在设备
        if not self.check_device_connected():
            raise ValueError("无法连接设备，请确认设备状态...")

        device_list = self.get_device_list()
        for dev_id in device_list:
            if self.check_root_command(dev_id) == "root":
                # 外销获取root权限
                self.export_root(dev_id)
                pass
            else:
                subprocess.run("adb -s " + dev_id + " vivoroot", shell=True)


    def check_device_connected(self):
        """检查是否有设备连接"""
        result = subprocess.run("adb devices", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                encoding="utf-8")
        lines = result.stdout.splitlines()
        # 如果输出的行数大于1，表示有设备连接
        if len(lines) > 1:
            return True
        else:
            return False

    def check_root_command(self, dev_id):
        # 判断内外销，手机，使用不同的命令

        strs = "adb shell getprop ro.vivo.product.version"
        strs = strs.replace("adb", "adb -s " + dev_id)
        result = self.user_run_adb_stdout(strs)
        version = result
        # 正则表达式，匹配 "W" 后面的数字
        pattern = r"W(\d+)"

        # 执行匹配操作
        matches = re.search(pattern, version)

        # 检查是否有匹配，如果有，打印匹配出的数字
        if matches:
            number_after_w = matches.group(1)
        else:
            print("内外销判断失败")
            return "vivoroot"
        if int(number_after_w) > 10:
            return "root"
        else:
            return "vivoroot"

    def export_root(self, dev_id):
        """ 外销获取root权限 """
        # 定义ADB命令前缀，包含设备ID
        adb_prefix = ["adb", "-s", dev_id]

        # 检查设备是否已root
        res = self.user_run_adb_stdout("adb -s " + dev_id + " root")
        if "adbd is already running as root" in res:
            return

        # 等待ADB设备连接
        subprocess.run(adb_prefix + ["wait-for-device"], check=True)

        # 获取并打印软件版本
        sw_version_result = subprocess.run(adb_prefix + ["shell", "getprop", "ro.vivo.product.version"],
                                           stdout=subprocess.PIPE, text=True, check=True)
        sw_version = sw_version_result.stdout.strip()

        # 获取vusbd_disabled属性值
        vusbd_disabled_result = subprocess.run(adb_prefix + ["shell", "getprop", "ro.vivo.disable.vusbd"],
                                               stdout=subprocess.PIPE, text=True, check=True)
        vusbd_disabled = int(vusbd_disabled_result.stdout.strip())

        # 根据vusbd_disabled的值决定如何Root设备
        if vusbd_disabled == 1:
            subprocess.run(["fastboot", "--version"], check=True)
            subprocess.run(adb_prefix + ["reboot", "bootloader"], check=True)
            subprocess.run(["fastboot", "vivoroot"], check=True)
            subprocess.run(["ping", "****", "-n", "2"], stdout=subprocess.DEVNULL)
            subprocess.run(["fastboot", "reboot"], check=True)
            subprocess.run(adb_prefix + ["wait-for-device"], check=True)
            subprocess.run(adb_prefix + ["root"], check=True)
            subprocess.run(["ping", "****", "-n", "2"], stdout=subprocess.DEVNULL)
        else:
            subprocess.run(adb_prefix + ["wait-for-device"], check=True)
            subprocess.run(adb_prefix + ["vivoroot"], check=True)
            subprocess.run(["ping", "****", "-n", "2"], stdout=subprocess.DEVNULL)

        while True:
            if "1" in self.is_system_status():
                time.sleep(10)
                return
            time.sleep(1)

    def check_adb_software_version(self):
        """检查adb软件版本_windows环境"""
        self.user_run_adb('adb shell getprop | findstr "ro.*.driver"')

    def get_device_name(self, device_id=None):
        """ 获取设备名称 """
        if device_id:
            return self.user_run_adb_stdout('adb -s ' + device_id + ' shell getprop ro.product.model')
        return self.user_run_adb_stdout('adb shell getprop ro.product.model')

    def get_fps(self, flag=False):
        """在windows环境下获取fps帧率"""
        # flag为True时，返回输出内容
        if flag:
            result = self.user_run_adb_stdout('adb shell dumpsys SurfaceFlinger | findstr "refresh-rate"')
            pattern = r"\d+"
            fps_arr = re.findall(pattern, result)
            fps = int(fps_arr[0])
            return fps
        else:
            self.user_run_adb_stdout('adb shell dumpsys SurfaceFlinger | findstr "refresh-rate"')

    def switch_brightness_255(self, brightness, device_id=None):
        """切换亮度_1-255"""
        if device_id:
            self.user_id_run_adb(dev_id=device_id, str='"settings put system screen_brightness ' + str(brightness) + '"')
            return
        self.user_run_adb('adb shell "settings put system screen_brightness ' + str(brightness) + '"')

    def reset_screen_policy(self):
        """重置屏幕策略"""
        subprocess.run('adb shell input keyevent KEYCODE_POWER', shell=True)
        time.sleep(1)
        self.on_menu()
        time.sleep(1)
        self.on_menu()

    # 唤醒屏幕
    def wake_up_screen(self):
        # subprocess.run('adb shell input keyevent KEYCODE_POWER', shell=True)
        # time.sleep(1)
        self.on_menu()
        time.sleep(1)
        self.on_menu()

    def is_system_status(self):
        """判断系统状态"""
        return self.user_run_adb_stdout("adb shell getprop sys.boot_completed")

    def get_ltpo_fps(self):
        """获取ltpo fps"""
        return self.user_run_adb_stdout("adb shell cat /sys/lcm/refresh_monitor")

    def install_atx_app(self, device_id=None):
        """
        安装ATX.apk应用，用于uiautomator2连接
        :param device_id: 设备ID
        :return: 是否安装成功
        """
        try:
            import os
            import sys
            
            def is_atx_installed(dev_id=None):
                # 判断ATX是否已安装，包名为com.github.uiautomator
                if dev_id:
                    cmd = f'adb -s {dev_id} shell pm list packages com.github.uiautomator'
                else:
                    cmd = 'adb shell pm list packages com.github.uiautomator'
                result = self.user_run_adb_stdout(cmd)
                return 'com.github.uiautomator' in result

            # 单设备
            if device_id:
                if is_atx_installed(device_id):
                    self.logger.info(f"设备 {device_id} 已安装ATX.apk，无需重复安装")
                    return True
            else:
                # 多设备，全部已安装才跳过
                all_installed = True
                device_list = self.get_device_list()
                for dev_id in device_list:
                    if not is_atx_installed(dev_id):
                        all_installed = False
                        break
                if all_installed and device_list:
                    self.logger.info("所有设备均已安装ATX.apk，无需重复安装")
                    return True

            # 检查多个可能的路径
            possible_paths = []
            
            # 1. 当前工作目录
            current_dir = os.getcwd()
            possible_paths.append(os.path.join(current_dir, "ATX.apk"))
            
            # 2. 应用程序根目录（针对PyInstaller）
            if getattr(sys, 'frozen', False):
                # 打包后的应用路径
                app_dir = os.path.dirname(sys.executable)
                possible_paths.append(os.path.join(app_dir, "ATX.apk"))
                
                # 有时在_internal目录下
                internal_dir = os.path.join(app_dir, "_internal")
                if os.path.exists(internal_dir):
                    possible_paths.append(os.path.join(internal_dir, "ATX.apk"))
            
            # 3. 脚本所在目录
            script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            possible_paths.append(os.path.join(script_dir, "ATX.apk"))
            
            # 查找有效的APK路径
            atx_apk_path = None
            for path in possible_paths:
                self.logger.info(f"尝试查找APK: {path}")
                if os.path.exists(path):
                    atx_apk_path = path
                    self.logger.info(f"找到ATX.apk文件: {atx_apk_path}")
                    break
            
            if not atx_apk_path:
                self.logger.error(f"在所有可能的路径中均未找到ATX.apk文件")
                return False
            
            # 指定设备ID或使用默认
            if device_id:
                # 安装APK
                cmd = f"adb -s {device_id} install -r \"{atx_apk_path}\""
                self.logger.info(f"安装ATX.apk: {cmd}")
                result = self.user_run_adb_stdout(cmd)
                return "Success" in result or "success" in result
            else:
                # 获取所有设备并安装
                success = True
                device_list = self.get_device_list()
                for dev_id in device_list:
                    cmd = f"adb -s {dev_id} install -r \"{atx_apk_path}\""
                    self.logger.info(f"安装ATX.apk到设备 {dev_id}: {cmd}")
                    result = self.user_run_adb_stdout(cmd)
                    if "Success" not in result and "success" not in result:
                        success = False
                
                return success and len(device_list) > 0
        except Exception as e:
            self.logger.error(f"安装ATX.apk失败: {e}")
            return False
