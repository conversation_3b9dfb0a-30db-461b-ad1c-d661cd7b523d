# 快速测试命令

## 问题修复

我已经修复了`PopupConfig`对象的方法调用问题。现在请重新启动API服务，然后使用以下命令测试：

## PowerShell快速测试

### 1. 基本测试命令
```powershell
# 测试健康检查API（应该成功）
Invoke-RestMethod -Uri "http://127.0.0.1:9080/api/status" -Method Get | ConvertTo-Json -Depth 5

# 测试功能验证API（现在应该不会报错）
Invoke-RestMethod -Uri "http://127.0.0.1:9080/api/validate" -Method Get | ConvertTo-Json -Depth 5

# 测试OCR验证API
Invoke-RestMethod -Uri "http://127.0.0.1:9080/api/validate/ocr" -Method Get | ConvertTo-Json -Depth 5

# 测试弹窗监控验证API
Invoke-RestMethod -Uri "http://127.0.0.1:9080/api/validate/popup" -Method Get | ConvertTo-Json -Depth 5
```

### 2. 使用测试脚本
```powershell
# 运行完整测试脚本
.\test_apis.ps1

# 运行详细测试（显示完整响应）
.\test_apis.ps1 -Detailed

# 测试不同的API地址
.\test_apis.ps1 -BaseUrl "http://localhost:9080/api"
```

### 3. 错误处理测试
```powershell
# 使用try-catch处理错误
try {
    $result = Invoke-RestMethod -Uri "http://127.0.0.1:9080/api/validate" -Method Get
    Write-Host "验证成功！总体状态: $($result.data.validation_summary.overall_status)" -ForegroundColor Green
    
    # 显示各模块状态
    foreach ($module in $result.data.results.PSObject.Properties) {
        $moduleName = $module.Name
        $moduleData = $module.Value
        $status = $moduleData.status
        $message = $moduleData.message
        
        $color = switch($status) {
            "success" { "Green" }
            "warning" { "Yellow" }
            "error" { "Red" }
            default { "White" }
        }
        
        Write-Host "$moduleName : $status - $message" -ForegroundColor $color
    }
} catch {
    Write-Host "API调用失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 尝试显示服务器返回的错误信息
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorContent = $reader.ReadToEnd()
        try {
            $errorJson = $errorContent | ConvertFrom-Json
            Write-Host "服务器错误: $($errorJson.message)" -ForegroundColor Yellow
        } catch {
            Write-Host "原始错误: $errorContent" -ForegroundColor Yellow
        }
    }
}
```

## 预期结果

修复后，您应该看到类似以下的成功响应：

### 健康检查API响应
```json
{
  "status": "success",
  "data": {
    "service": {
      "name": "BrightnessReductionAutomation API",
      "version": "3.0.0",
      "status": "running",
      "start_time": "2025-08-20T10:59:21.731682",
      "uptime_seconds": 300
    },
    "health": "healthy",
    "devices": {
      "list": ["10AF3F0C1C001NP"],
      "count": 1,
      "connected": true
    }
  }
}
```

### 功能验证API响应
```json
{
  "status": "success",
  "data": {
    "validation_summary": {
      "overall_status": "success",
      "test_counts": {
        "total": 2,
        "success": 2,
        "warning": 0,
        "error": 0
      }
    },
    "results": {
      "ocr": {
        "status": "success",
        "message": "OCR功能验证成功"
      },
      "popup_monitor": {
        "status": "success",
        "message": "弹窗监控功能验证成功"
      }
    }
  }
}
```

## 故障排除

如果仍然遇到问题：

1. **重启API服务**：确保使用修复后的代码
2. **检查依赖**：确保所有必要的文件存在
3. **查看日志**：检查API服务的日志输出
4. **网络连接**：确认API服务正在监听9080端口

## 下一步

测试成功后，您可以：
1. 集成这些API到您的监控系统
2. 设置定期健康检查
3. 在部署流程中添加功能验证
4. 根据需要扩展验证功能
