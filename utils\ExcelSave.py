from openpyxl import load_workbook, Workbook
from openpyxl.styles import PatternFill, Font
from utils.adb.ExAdb import ExAdb
from utils.Util import Util
import os
import time
import threading


class ExcelSave:
    # 类级别的文件锁，用于多线程环境下的文件访问控制
    _file_locks = {}
    _lock_dict_lock = threading.Lock()

    def __init__(self, device_id, filename=None, sheet_name=None, append_mode=True):
        """
        初始化ExcelSave实例

        Args:
            device_id (str): 设备ID
            filename (str, optional): Excel文件名。默认为None
            sheet_name (str, optional): 工作表名称。默认为None，会使用"Data"
            append_mode (bool, optional): 是否为追加模式，追加模式下不会删除现有工作表。默认为True
        """
        self.adb = ExAdb()
        self.device_id = device_id
        self.filename = filename
        self.sheet_name = sheet_name or "Data"
        self.util = Util()
        self._needs_metadata = True  # 标记是否需要添加元数据
        self.workbook = None
        self.worksheet = None
        # 追加模式标志，True表示在现有工作表上追加数据，False表示重新创建工作表
        self.append_mode = append_mode

        if filename:
            try:
                self.load_existing_excel(filename)
            except FileNotFoundError:
                # 文件不存在时直接创建新文件，不再显示警告
                self.workbook = Workbook()
                # 删除默认的Sheet工作表
                if "Sheet" in self.workbook.sheetnames:
                    del self.workbook["Sheet"]
                self.reset_worksheet()
        else:
            self.workbook = Workbook()
            # 删除默认的Sheet工作表
            if "Sheet" in self.workbook.sheetnames:
                del self.workbook["Sheet"]
            self.reset_worksheet()

    @classmethod
    def get_file_lock(cls, filename):
        """获取指定文件的锁"""
        with cls._lock_dict_lock:
            if filename not in cls._file_locks:
                cls._file_locks[filename] = threading.Lock()
            return cls._file_locks[filename]

    def load_existing_excel(self, filename):
        """
        加载现有Excel文件，支持多次尝试

        Args:
            filename (str): 要加载的Excel文件路径

        Raises:
            FileNotFoundError: 文件不存在
            PermissionError: 无法访问文件
            Exception: 其他异常
        """
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            try:
                # 获取文件锁
                with self.get_file_lock(filename):
                    # 首先尝试直接加载文件
                    if os.path.exists(filename):
                        self.workbook = load_workbook(filename)
                    else:
                        # 如果文件不存在，尝试在同名文件夹中查找
                        basename = os.path.basename(filename)
                        dirname = os.path.dirname(filename)
                        folder_name = os.path.splitext(basename)[0]
                        alternative_path = os.path.join(dirname, folder_name, basename)
                        
                        if os.path.exists(alternative_path):
                            self.workbook = load_workbook(alternative_path)
                            # 更新文件名为找到的路径
                            self.filename = alternative_path
                        else:
                            raise FileNotFoundError(f"文件 {filename} 和 {alternative_path} 均不存在")

                    # 删除默认的Sheet工作表（如果存在）
                    if "Sheet" in self.workbook.sheetnames and len(self.workbook.sheetnames) > 1:
                        del self.workbook["Sheet"]

                    # 检查是否已有指定名称的工作表
                    if self.sheet_name in self.workbook.sheetnames:
                        self.worksheet = self.workbook[self.sheet_name]
                        # 追加模式下保留现有工作表，非追加模式则重置工作表
                        if not self.append_mode:
                            print(f"非追加模式: 重置工作表 {self.sheet_name}")
                            self.reset_worksheet()
                        else:
                            print(f"追加模式: 使用现有工作表 {self.sheet_name}")
                            self._needs_metadata = False  # 已有工作表，假设元数据已存在
                    else:
                        # 创建新的工作表
                        print(f"创建新工作表: {self.sheet_name}")
                        self.worksheet = self.workbook.create_sheet(title=self.sheet_name)
                        self.set_headers()
                        self._needs_metadata = True  # 新工作表需要元数据

                    return  # 成功加载，退出循环

            except PermissionError:
                # 文件可能被其他进程锁定，等待后重试
                attempt += 1
                if attempt < max_attempts:
                    time.sleep(1)  # 等待1秒后重试
                else:
                    raise PermissionError(f"无法访问文件 {filename}，已尝试 {max_attempts} 次")
            except FileNotFoundError:
                # 文件不存在，创建新的工作簿
                self.workbook = Workbook()
                # 删除默认的Sheet工作表
                if "Sheet" in self.workbook.sheetnames:
                    del self.workbook["Sheet"]
                self.reset_worksheet()
                return
            except Exception as e:
                # 其他异常直接抛出
                raise e

    def reset_worksheet(self):
        """
        重置或创建工作表

        如果已存在同名工作表，会先删除再创建。
        重置后会设置表头并标记需要添加元数据。
        """
        # 检查是否已有指定名称的工作表
        if self.sheet_name in self.workbook.sheetnames:
            # 删除现有工作表
            del self.workbook[self.sheet_name]

        # 创建新工作表
        self.worksheet = self.workbook.create_sheet(title=self.sheet_name)
        self.set_headers()
        self._needs_metadata = True  # 新工作表需要元数据

    def set_headers(self):
        """设置工作表表头"""
        self.worksheet.append(["场景", "亮度", "降幅", "期望值", "测试结果"])

    def append_dict_to_excel(self, data, current_nit=None, is_headers=False, add_separator=False, start_row=None):
        """
        将字典数据追加到Excel工作表中

        Args:
            data (dict): 要追加的数据字典
            current_nit (int/float, optional): 当前亮度值。默认为None
            add_separator (bool, optional): 是否添加分隔符。默认为False
            start_row (int, optional): 从指定行开始追加（None表示从最后一行追加）
        """
        if self.worksheet is None:
            self.reset_worksheet()

        if is_headers:
            # 如果工作表已经有内容（不只是一个初始表头），则添加5行空白作为分隔
            if self.worksheet.max_row > 1:
                for _ in range(start_row):
                    self.worksheet.append([])
            # 添加表头
            self.set_headers()

        # 添加数据行
        for name, values in data.items():
            nit_value = values.get("nit", "")
            drop_value = values.get("drop", "")
            standard_value = values.get("standard", "")
            result_value = "合格" if values.get("result", "") else "不合格"
            self.worksheet.append([name, nit_value, drop_value, standard_value, result_value])

            last_row = self.worksheet.max_row
            result_cell = self.worksheet.cell(row=last_row, column=5)  # 仅更改测试结果列的样式
            if result_value == "不合格":
                result_cell.font = Font(color="FF0000")  # 红色字体
            else:
                result_cell.font = Font(color="00FF00")  # 绿色字体

        # 如果需要添加分隔符（用于不同亮度值之间的分隔）
        if add_separator and current_nit is not None:
            # 再添加一行空行
            self.worksheet.append([])
            # 添加亮度标记行
            self.worksheet.append([f"亮度值: {current_nit} nit", "", "", "", ""])
            # 添加5行空行
            for _ in range(5):
                self.worksheet.append([])

    def _add_metadata(self, sheet, current_nit=None):
        """
        向指定的工作表添加元数据的私有方法

        Args:
            sheet (Worksheet): 要添加元数据的工作表
            current_nit (int/float, optional): 当前亮度值。默认为None
        """
        sheet.append([])
        auto_brightness_status = "关"
        if self.adb.get_auto_brightness(device_id=self.device_id) == "1":
            auto_brightness_status = "开"

        sheet.append(["测试人", self.util.get_user_name(), ""])
        sheet.append(["测试日期", self.util.get_current_date(), ""])
        if current_nit:
            sheet.append(["当前亮度", current_nit, ""])
        sheet.append(["自动亮度", auto_brightness_status, ""])
        sheet.append(["设备名称", self.adb.get_device_name(device_id=self.device_id), ""])
        sheet.append(["软件版本", self.adb.get_software_version(device_id=self.device_id), ""])

    # <--- 新增/修改 开始 --->
    def add_metadata_to_sheet(self, sheet_name=None, current_nit=None):
        """
        给指定的工作表添加元数据。
        如果未指定sheet_name，则默认添加到当前实例关联的工作表 (self.sheet_name)。

        Args:
            sheet_name (str, optional): 目标工作表的名称。如果为None，则使用 self.sheet_name。
            current_nit (int/float, optional): 当前亮度值。默认为None。
        """
        # 确定目标工作表
        target_sheet_name = sheet_name or self.sheet_name

        if target_sheet_name not in self.workbook.sheetnames:
            print(f"警告: 找不到名为 '{target_sheet_name}' 的工作表，无法添加元数据。")
            return

        # 获取工作表对象
        sheet = self.workbook[target_sheet_name]

        # 调用私有方法添加元数据
        self._add_metadata(sheet, current_nit)

    # <--- 新增/修改 结束 --->

    def add_metadata_to_all_sheets(self, current_nit=None):
        """
        给所有工作表添加元数据 (保留此方法用于兼容或特殊需求)

        Args:
            current_nit (int/float, optional): 当前亮度值。默认为None
        """
        # 遍历所有工作表
        for sheet_name in self.workbook.sheetnames:
            sheet = self.workbook[sheet_name]
            self._add_metadata(sheet, current_nit)

    def get_all_data(self):
        """
        获取工作表中的所有数据

        Returns:
            list: 工作表中的所有数据行
        """
        if self.worksheet is None:
            return []

        data = []
        for row in self.worksheet.iter_rows(values_only=True):
            if any(cell for cell in row):  # 只添加非空行
                data.append(row)

        return data

    def save(self, filename=None):
        """
        保存Excel文件，支持多次尝试和文件锁
        会在保存前创建与文件同名的文件夹，并将文件保存在该文件夹中
        如果文件已经在同名文件夹中，则直接保存不再创建新文件夹

        Args:
            filename (str, optional): 要保存的文件路径。如果为None，则使用初始化时设置的文件名

        Raises:
            ValueError: 未指定要保存的文件名
            PermissionError: 无法保存文件
            Exception: 其他异常
        """
        # 确定最终要保存的文件名
        save_filename = filename if filename is not None else self.filename
        if not save_filename:
            raise ValueError("未指定要保存的文件名")

        # 准备文件路径
        basename = os.path.basename(save_filename)
        dirname = os.path.dirname(save_filename)
        folder_name = os.path.splitext(basename)[0]
        
        # 检查文件是否已经在同名文件夹中
        parent_dir = os.path.basename(dirname)
        final_save_path = save_filename
        
        if parent_dir != folder_name:
            # 文件不在同名文件夹中，需要创建文件夹并更新路径
            folder_path = os.path.join(dirname, folder_name)
            os.makedirs(folder_path, exist_ok=True)
            
            # 更新保存路径为文件夹内的文件
            final_save_path = os.path.join(folder_path, basename)
            
            # 更新当前实例的文件名，以便后续操作使用正确的路径
            self.filename = final_save_path
        else:
            # 文件已在同名文件夹中，直接使用原路径
            print(f"文件已在同名文件夹中: {save_filename}")

        # 删除默认的Sheet工作表（如果存在且不是唯一的工作表）
        if "Sheet" in self.workbook.sheetnames and len(self.workbook.sheetnames) > 1:
            del self.workbook["Sheet"]

        # 使用文件锁和多次尝试机制保存文件
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            try:
                # 获取文件锁
                with self.get_file_lock(final_save_path):
                    # 如果文件已存在且我们要添加新的sheet，先加载现有文件
                    if os.path.exists(final_save_path) and self.sheet_name not in self.workbook.sheetnames:
                        try:
                            existing_wb = load_workbook(final_save_path)

                            # 删除默认的Sheet工作表（如果存在且不是唯一的工作表）
                            if "Sheet" in existing_wb.sheetnames and len(existing_wb.sheetnames) > 1:
                                del existing_wb["Sheet"]

                            # 将现有工作簿的所有工作表复制到当前工作簿
                            for sheet_name in existing_wb.sheetnames:
                                if sheet_name not in self.workbook.sheetnames:
                                    # 复制工作表
                                    print(f"合并工作表: 从现有文件复制工作表 {sheet_name}")
                                    new_sheet = self.workbook.create_sheet(title=sheet_name)
                                    existing_sheet = existing_wb[sheet_name]

                                    # 复制单元格内容和样式
                                    for row in existing_sheet.iter_rows():
                                        for cell in row:
                                            new_cell = new_sheet.cell(row=cell.row, column=cell.column)
                                            new_cell.value = cell.value
                                            if cell.has_style:
                                                new_cell.font = cell.font
                                                new_cell.border = cell.border
                                                new_cell.fill = cell.fill
                                                new_cell.number_format = cell.number_format
                                                new_cell.protection = cell.protection
                                                new_cell.alignment = cell.alignment
                        except Exception as e:
                            print(f"合并工作簿时出错: {e}")

                    # 最后一次检查并删除默认的Sheet工作表
                    if "Sheet" in self.workbook.sheetnames and len(self.workbook.sheetnames) > 1:
                        del self.workbook["Sheet"]

                    # 保存工作簿
                    print(f"保存工作簿到文件: {final_save_path}")
                    self.workbook.save(final_save_path)
                    return  # 成功保存，退出循环

            except PermissionError:
                # 文件可能被其他进程锁定，等待后重试
                attempt += 1
                if attempt < max_attempts:
                    time.sleep(1)  # 等待1秒后重试
                else:
                    # 尝试使用备用文件名
                    backup_filename = f"{os.path.splitext(basename)[0]}_backup_{int(time.time())}.xlsx"
                    # 确保备份文件也保存在同名文件夹中
                    folder_path = os.path.dirname(final_save_path)
                    backup_path = os.path.join(folder_path, backup_filename)
                    print(f"无法保存到 {final_save_path}，尝试保存到备用文件 {backup_path}")
                    try:
                        # 最后一次检查并删除默认的Sheet工作表
                        if "Sheet" in self.workbook.sheetnames and len(self.workbook.sheetnames) > 1:
                            del self.workbook["Sheet"]
                        self.workbook.save(backup_path)
                        print(f"已成功保存到备用文件 {backup_path}")
                        return
                    except Exception as e:
                        raise PermissionError(f"无法保存文件，原始错误: {e}")
            except Exception as e:
                # 其他异常，尝试使用备用文件名
                backup_filename = f"{os.path.splitext(basename)[0]}_error_{int(time.time())}.xlsx"
                # 确保备份文件也保存在同名文件夹中
                folder_path = os.path.dirname(final_save_path)
                backup_path = os.path.join(folder_path, backup_filename)
                print(f"保存时发生错误: {e}，尝试保存到备用文件 {backup_path}")
                try:
                    # 最后一次检查并删除默认的Sheet工作表
                    if "Sheet" in self.workbook.sheetnames and len(self.workbook.sheetnames) > 1:
                        del self.workbook["Sheet"]
                    self.workbook.save(backup_path)
                    print(f"已成功保存到备用文件 {backup_path}")
                    return
                except Exception as e2:
                    raise Exception(f"无法保存文件，原始错误: {e}，备用保存错误: {e2}")


# <--- 新增/修改 开始 (更新示例代码) --->
if __name__ == '__main__':
    # 1. 初始化 ExcelSave 实例
    # 参数说明:
    # - device_id: 设备ID (必填)
    # - filename: Excel文件名 (可选，不填则创建新文件)
    # - sheet_name: 工作表名称 (可选，默认为"Data")
    # - append_mode: 是否为追加模式 (默认为True)
    filename = 'test_report.xlsx'
    excel_save = ExcelSave(
        device_id="your_device_id",
        filename=filename,  # 如果文件不存在会自动创建
        sheet_name="自动亮度",
        append_mode=True
    )
    excel_save.add_metadata_to_sheet(
        sheet_name="手动亮度"
    )
    excel_save.save(filename)
    exit()

    # 2. 准备测试数据
    test_data_1 = {
        "场景1": {
            "nit": 200,  # 亮度值
            "drop": 5,  # 降幅
            "standard": 10,  # 期望值
            "result": True  # 测试结果(True=合格, False=不合格)
        },
        "场景2": {
            "nit": 180,
            "drop": 8,
            "standard": 10,
            "result": True
        },
        "场景3": {
            "nit": 150,
            "drop": 12,
            "standard": 10,
            "result": False  # 不合格会显示为红色
        }
    }

    test_data_2 = {
        "场景4": {
            "nit": 250,
            "drop": 6,
            "standard": 10,
            "result": True
        },
        "场景5": {
            "nit": 220,
            "drop": 9,
            "standard": 10,
            "result": True
        },
        "场景6": {
            "nit": 240,
            "drop": 12,
            "standard": 10,
            "result": True
        }
    }

    # 3. 添加数据到Excel
    # 第一次添加数据，会创建表头
    excel_save.append_dict_to_excel(
        data=test_data_1,
        current_nit=200,
        add_separator=True
    )

    # 添加分隔符和新的亮度值标记
    excel_save.append_dict_to_excel(
        test_data_2,
        current_nit=250,
        is_headers=True,  # 假设这是另一组测试，需要新的表头
        add_separator=True
    )

    # 4. 添加元数据(可选)
    # 使用新方法，只向当前工作的sheet("亮度测试")添加元数据
    print(f"\n向工作表 '{excel_save.sheet_name}' 添加元数据...")
    excel_save.add_metadata_to_sheet(current_nit=250)

    # 如果需要向另一个特定的sheet添加元数据，可以这样做（假设存在一个叫'功耗测试'的sheet）:
    # excel_save.workbook.create_sheet("功耗测试") # 先创建
    # excel_save.add_metadata_to_sheet(sheet_name="功耗测试", current_nit=250)

    # 5. 保存Excel文件
    try:
        excel_save.save()  # 使用初始化时指定的文件名保存
        # 或者指定新文件名保存
        # excel_save.save("new_report.xlsx")
        print("Excel文件保存成功")
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")

    # 6. 读取数据示例(可选)
    all_data = excel_save.get_all_data()
    print("\nExcel中的数据:")
    for row in all_data:
        print(row)
# <--- 新增/修改 结束 --->