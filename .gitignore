.venv
build
dist
/logs
*.txt
*.xlsx
/utils/logs
__pycache__
/controller/__pycache__
/queues/__pycache__
/utils/__pycache__
/enums/__pycache__
/qgui/__pycache__
/api/__pycache__

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store

# Task files
# tasks.json
# tasks/ 