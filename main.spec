# -*- mode: python ; coding: utf-8 -*-

# ======================
# 基础配置
# ======================
import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 禁用UPX压缩（某些防病毒软件会误报）
upx_enabled = False

# ======================
# 路径配置
# ======================
# 项目根目录
project_root = r'F:\pythonProject\BrightnessReductionAutomation'

# 虚拟环境路径（确认是否使用 .venv 或 venv）
venv_path = os.path.join(project_root, '.venv')

# 自动检测虚拟环境有效性
if not os.path.exists(venv_path):
    raise FileNotFoundError(f"虚拟环境路径不存在: {venv_path}")

# 平台兼容的site-packages路径
if sys.platform == 'win32':
    site_packages = os.path.join(venv_path, 'Lib', 'site-packages')
else:
    site_packages = os.path.join(venv_path, 'lib', f'python{sys.version_info.major}.{sys.version_info.minor}', 'site-packages')

# ======================
# 二进制和数据文件配置
# ======================
def validate_path(path, description):
    """路径验证装饰器"""
    if not os.path.exists(path):
        raise FileNotFoundError(f"{description}路径不存在: {path}")
    return path

# DLL文件收集
dll_files = []
dll_dir = os.path.join(project_root)
if os.path.exists(dll_dir):
    for fname in os.listdir(dll_dir):
        if fname.endswith('.dll'):
            src = os.path.join(dll_dir, fname)
            dll_files.append((src, '.'))

# 项目目录结构
project_folders = [
    'component', 'controller',
    'enums', 'qgui', 'queues', 'utils'
]

# ======================
# JSON文件收集系统
# ======================
def collect_json_files(source_dir, target_dir='.'):
    """递归收集JSON文件并保持目录结构"""
    json_files = []
    for root, _, files in os.walk(source_dir):
        for file in files:
            if file.endswith('.json') and not file.startswith('temp_'):
                src_path = os.path.join(root, file)
                # 计算相对路径
                rel_path = os.path.relpath(root, source_dir)
                dest_path = os.path.join(target_dir, rel_path)
                json_files.append((src_path, dest_path))
    return json_files

# 收集各目录JSON文件
json_data = []
# 1. 收集根目录JSON
root_json = [ (os.path.join(project_root, f), '.')
            for f in os.listdir(project_root)
            if f.endswith('.json') ]
# 2. 收集模块目录JSON
for folder in project_folders + ['config']:
    module_dir = os.path.join(project_root, folder)
    if os.path.exists(module_dir):
        json_data += collect_json_files(module_dir, folder)

# 添加APK文件到打包列表
apk_file = os.path.join(project_root, 'ATX.apk')
if os.path.exists(apk_file):
    root_json.append((apk_file, '.'))

# 手动复制uiautomator2资源到根目录
try:
    import uiautomator2
    u2_path = os.path.dirname(uiautomator2.__file__)
    assets_path = os.path.join(u2_path, 'assets')
    if os.path.exists(assets_path):
        # 复制关键文件到根目录
        for file_name in ['u2.jar', 'app-uiautomator.apk', 'app-uiautomator-test.apk']:
            src_file = os.path.join(assets_path, file_name)
            if os.path.exists(src_file):
                datas.append((src_file, '.'))
except Exception as e:
    print(f"警告: 无法复制uiautomator2资源文件 - {str(e)}")

# ======================
# 初始化数据文件
# ======================
datas = dll_files.copy() + root_json + json_data

# 添加OCR数据文件
tessdata_src = os.path.join(project_root, 'utils', 'ocr', 'tessdata')
if os.path.exists(tessdata_src):
    datas.append((tessdata_src, os.path.join('utils', 'ocr', 'tessdata')))

# 手动添加关键源代码文件
src_files = [
    ('queues/TestQueue.py', 'queues'),
    ('utils/GetBrightnessReduction.py', 'utils')
]
for src_file in src_files:
    src_path = os.path.join(project_root, src_file[0])
    if os.path.exists(src_path):
        datas.append((src_path, src_file[1]))

# ======================
# qgui资源文件收集
# ======================
def collect_resource_files(source_dir, target_dir):
    """递归收集资源文件并保持目录结构"""
    resource_files = []
    for root, _, files in os.walk(source_dir):
        for file in files:
            src_path = os.path.join(root, file)
            # 计算相对路径
            rel_path = os.path.relpath(root, os.path.dirname(source_dir))
            dest_path = os.path.join(target_dir, rel_path)
            resource_files.append((src_path, dest_path))
    return resource_files

# 添加qgui资源文件
qgui_resources_src = os.path.join(project_root, 'qgui', 'resources')
if os.path.exists(qgui_resources_src):
    qgui_resources = collect_resource_files(qgui_resources_src, 'qgui')
    datas.extend(qgui_resources)

# 添加qgui主题文件
qgui_theme_src = os.path.join(project_root, 'qgui', 'theme')
if os.path.exists(qgui_theme_src):
    qgui_theme = collect_resource_files(qgui_theme_src, 'qgui')
    datas.extend(qgui_theme)

# ======================
# 第三方库特殊处理
# ======================
# ttkbootstrap
ttkbootstrap_path = os.path.join(site_packages, 'ttkbootstrap')
if os.path.exists(ttkbootstrap_path):
    datas.append((ttkbootstrap_path, 'ttkbootstrap'))

# Tkinter运行时文件
try:
    datas += collect_data_files('tkinter')
except Exception as e:
    print(f"警告: Tkinter数据文件收集失败 - {str(e)}")

# Pillow (PIL)
pil_data = collect_data_files('PIL', include_py_files=True)
datas += pil_data

# Flask相关数据文件
try:
    flask_data = collect_data_files('flask', include_py_files=False)
    datas += flask_data
    print("已添加Flask数据文件")
except Exception as e:
    print(f"警告: Flask数据文件收集失败 - {str(e)}")

# Werkzeug数据文件
try:
    werkzeug_data = collect_data_files('werkzeug', include_py_files=False)
    datas += werkzeug_data
    print("已添加Werkzeug数据文件")
except Exception as e:
    print(f"警告: Werkzeug数据文件收集失败 - {str(e)}")

# Jinja2数据文件
try:
    jinja2_data = collect_data_files('jinja2', include_py_files=False)
    datas += jinja2_data
    print("已添加Jinja2数据文件")
except Exception as e:
    print(f"警告: Jinja2数据文件收集失败 - {str(e)}")

# uiautomator2资源文件
try:
    import uiautomator2
    u2_path = os.path.dirname(uiautomator2.__file__)
    
    # 添加assets目录
    u2_assets_path = os.path.join(u2_path, 'assets')
    if os.path.exists(u2_assets_path):
        for asset_file in os.listdir(u2_assets_path):
            src = os.path.join(u2_assets_path, asset_file)
            if os.path.isfile(src):
                datas.append((src, os.path.join('uiautomator2', 'assets')))
    
    # 添加static目录
    u2_static_path = os.path.join(u2_path, 'static')
    if os.path.exists(u2_static_path):
        datas.append((u2_static_path, os.path.join('uiautomator2', 'static')))
    
    # 添加adbutils
    try:
        import adbutils
        adbutils_path = os.path.dirname(adbutils.__file__)
        # 添加binaries
        datas += collect_data_files('adbutils', include_py_files=True)
    except ImportError:
        print("警告: 无法导入adbutils")
    
    # 添加u2init
    try:
        import u2init
        datas += collect_data_files('u2init', include_py_files=True)
    except ImportError:
        print("警告: 无法导入u2init")
    
    print(f"已添加uiautomator2资源文件")
except Exception as e:
    print(f"警告: uiautomator2资源文件收集失败 - {str(e)}")

# ======================
# XML模块配置
# ======================
binaries = []
if sys.platform == 'win32':
    pyexpat_path = os.path.join(site_packages, 'pyexpat.pyd')
    if os.path.exists(pyexpat_path):
        binaries.append((pyexpat_path, '.'))

# ======================
# 隐藏导入配置
# ======================
hiddenimports = []

# Pillow子模块
hiddenimports += collect_submodules('PIL')
hiddenimports += [
    'PIL._imaging',
    'PIL._imagingcms',
    'PIL._imagingft',
]

# Tkinter相关
hiddenimports += [
    'tkinter',
    '_tkinter',
    'tkinter.filedialog',
    'tkinter.messagebox'
]

# XML相关
hiddenimports += [
    'xml.parsers.expat',
    'xml.dom',
    'xml.sax',
    'xml.etree',
    'xml.etree.ElementTree'
]

# uiautomator2相关
hiddenimports += [
    'uiautomator2',
    'uiautomator2.init',
    'uiautomator2.utils',
    'uiautomator2.ext.htmlreport',
    'adbutils',
    'apkutils2',
    'deprecation',
    'packaging',
    'packaging.version',
    'urllib3',
    'urllib3.contrib',
    'urllib3.contrib.socks',
    'filelock',
    'requests',
    'requests.packages',
    'requests.packages.urllib3',
    'PIL.ImageColor',
    'PIL.ImageDraw',
    # 添加错误处理和类型转换相关模块
    're',
    'json',
    'logging',
    'warnings',
    'queues.TestQueue',
    'utils.GetBrightnessReduction',
    # 添加所有queues模块和utils模块
    'queues',
    'utils',
    'utils.adb',
    'utils.adb.BaseAdb',
    'utils.adb.ExAdb',
    'utils.adb.OpenApp',
    'component',
    'controller',
    'enums',
    'enums.PackageName',
    'config',
    # 添加线程和队列相关模块
    'threading',
    'queue',
    'subprocess',
    'time',
    'datetime',
    'math',
    'os',
    'sys'
]

# Flask API服务相关
hiddenimports += [
    # Flask核心模块
    'flask',
    'flask.app',
    'flask.blueprints',
    'flask.ctx',
    'flask.globals',
    'flask.helpers',
    'flask.json',
    'flask.sessions',
    'flask.signals',
    'flask.templating',
    'flask.wrappers',
    # Werkzeug依赖（Flask的核心依赖）
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'werkzeug.exceptions',
    'werkzeug.routing',
    'werkzeug.wrappers',
    'werkzeug.datastructures',
    'werkzeug.security',
    'werkzeug.urls',
    'werkzeug.http',
    # Jinja2模板引擎
    'jinja2',
    'jinja2.runtime',
    'jinja2.loaders',
    'jinja2.environment',
    'jinja2.compiler',
    # 其他Flask依赖
    'itsdangerous',
    'click',
    'blinker',
    'blinker.base'
]

# 网络请求库完善
hiddenimports += [
    # requests库子模块
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.models',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    'requests.exceptions',
    'requests.hooks',
    'requests.status_codes',
    # urllib3库子模块
    'urllib3.poolmanager',
    'urllib3.connectionpool',
    'urllib3.util',
    'urllib3.util.retry',
    'urllib3.util.ssl_',
    'urllib3.util.timeout',
    'urllib3.util.connection',
    'urllib3.util.url',
    'urllib3.response',
    'urllib3.exceptions',
    'urllib3.fields',
    'urllib3.filepost',
    # 其他网络相关
    'certifi',
    'charset_normalizer',
    'charset_normalizer.api',
    'charset_normalizer.constant',
    'idna',
    'idna.core',
    'idna.codec'
]

try:
    hiddenimports += collect_submodules('uiautomator2')
    hiddenimports += collect_submodules('adbutils')
except Exception as e:
    print(f"警告: 无法收集uiautomator2或adbutils子模块 - {str(e)}")

# 收集Flask相关库的所有子模块
try:
    hiddenimports += collect_submodules('flask')
    print("已收集Flask子模块")
except Exception as e:
    print(f"警告: 无法收集Flask子模块 - {str(e)}")

try:
    hiddenimports += collect_submodules('werkzeug')
    print("已收集Werkzeug子模块")
except Exception as e:
    print(f"警告: 无法收集Werkzeug子模块 - {str(e)}")

try:
    hiddenimports += collect_submodules('jinja2')
    print("已收集Jinja2子模块")
except Exception as e:
    print(f"警告: 无法收集Jinja2子模块 - {str(e)}")

try:
    hiddenimports += collect_submodules('requests')
    print("已收集requests子模块")
except Exception as e:
    print(f"警告: 无法收集requests子模块 - {str(e)}")

try:
    hiddenimports += collect_submodules('urllib3')
    print("已收集urllib3子模块")
except Exception as e:
    print(f"警告: 无法收集urllib3子模块 - {str(e)}")

# ======================
# 分析配置
# ======================
a = Analysis(
    ['main.py'],
    pathex=[project_root],  # 添加项目根目录到搜索路径
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['unittest', 'test'],  # 排除测试模块
    noarchive=False,
    optimize=0,  # 禁用优化以保留更多调试信息
    cipher=None,
)

# ======================
# 构建配置
# ======================
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    exclude_binaries=True,
    name='BrightnessDropAuto',  # 统一命名
    debug=True,  # 保持调试模式开启
    bootloader_ignore_signals=False,
    strip=False,
    upx=upx_enabled,
    console=True,  # 保持控制台开启以查看错误
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=upx_enabled,
    name='BrightnessDropAuto',
)
