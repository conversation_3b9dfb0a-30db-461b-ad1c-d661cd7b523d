import json
import os


class AppConfig:
    """应用配置管理类，用于从JSON文件中读取应用配置信息"""
    def __init__(self, config_path="app_config.json", version="5.0"):
        """
        初始化应用配置管理类
        :param config_path: 配置文件路径
        :param version: 使用的标准版本 4.0 或 5.0
        """
        self.config_path = config_path
        self.version = version
        self.app_config = self._load_config()
    
    def _load_config(self):
        """加载JSON配置文件"""
        if not os.path.exists(self.config_path):
            # 如果配置文件不存在，尝试在_internal目录下查找
            internal_path = os.path.join("_internal", self.config_path)
            if os.path.exists(internal_path):
                self.config_path = internal_path
            else:
                # 如果配置文件不存在，返回空字典
                return {}
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as json_file:
                return json.load(json_file)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def get_app_by_desc(self, desc):
        """根据应用描述获取应用配置"""
        for key, app_info in self.app_config.items():
            if app_info.get("desc") == desc:
                return app_info
        return None
    
    def get_app_by_key(self, key):
        """根据应用键名获取应用配置"""
        return self.app_config.get(key, None)
    
    def get_app_package(self, desc):
        """获取应用包名"""
        app_info = self.get_app_by_desc(desc)
        if app_info:
            return app_info.get("package", "")
        return desc if "." in desc else ""  # 如果desc本身就是包名，则直接返回
    
    def get_app_activity(self, desc):
        """获取应用Activity"""
        app_info = self.get_app_by_desc(desc)
        if app_info:
            return app_info.get("activity", "")
        return ""
    
    def get_app_name(self, desc):
        """获取应用实际名称(用于UI查找)"""
        app_info = self.get_app_by_desc(desc)
        if app_info:
            return app_info.get("app_name", desc)
        return desc
    
    def get_open_method(self, desc):
        """获取应用打开方法"""
        app_info = self.get_app_by_desc(desc)
        if app_info:
            return app_info.get("open_method", "package")
        
        # 对于特殊应用，返回special
        special_apps = ["桌面", "系统桌面", "负一屏", "微信视频"]
        if desc in special_apps:
            return "special"
            
        return "package"  # 默认使用package方式打开
    
    def get_key_by_desc(self, desc):
        """根据应用描述获取键名"""
        for key, app_info in self.app_config.items():
            if app_info.get("desc") == desc:
                return key
        return None
    
    def get_apps_by_type(self, type_name):
        """根据应用类型获取应用列表"""
        result = []
        for key, app_info in self.app_config.items():
            if type_name in app_info.get("type", []):
                result.append(app_info.get("desc"))
        return result
    
    def get_standard_value(self, desc, is_indoor=True, is_auto=True):
        """
        获取标准降幅值
        :param desc: 应用描述
        :param is_indoor: 是否室内标准
        :param is_auto: 是否自动亮度
        :return: 标准降幅值
        """
        app_info = self.get_app_by_desc(desc)
        if not app_info:
            return "0"
        
        # 获取对应版本的标准
        standards = app_info.get("standards", {}).get(self.version, {})
        
        # 根据室内/室外选择标准
        if is_indoor:
            standard = standards.get("indoor", "0/0")
        else:
            standard = standards.get("outdoor", "0/0")
        
        # 根据自动/手动亮度选择标准
        if is_auto:
            return standard.split("/")[0]
        else:
            return standard.split("/")[1]
    
    def set_version(self, version):
        """设置使用的标准版本"""
        self.version = version
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as json_file:
                json.dump(self.app_config, json_file, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def update_app_config(self, key, new_value):
        """更新应用配置"""
        if key in self.app_config:
            self.app_config[key] = new_value
            return self.save_config()
        return False
    
    def add_app_config(self, key, value):
        """添加应用配置"""
        if key not in self.app_config:
            self.app_config[key] = value
            return self.save_config()
        return False 