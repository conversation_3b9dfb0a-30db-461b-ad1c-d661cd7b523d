# 亮度测试API客户端库

一个用于与亮度调节自动化程序API服务交互的Python客户端库。提供简洁易用的接口，支持所有预设配置和高级功能。

## 🚀 特性

- **完整API封装**：封装所有API接口，包括健康检查、队列管理、设备管理、任务监控
- **预设方法**：为每个预设配置提供专门的方法，一键调用
- **高级功能**：任务状态监控、批量操作、错误处理、重试机制
- **Python 3.7+兼容**：确保在Python 3.7及以上版本正常工作
- **类型安全**：提供完整的类型注解和数据模型
- **易于集成**：简洁的API设计，方便第三方应用程序集成

## 📦 安装

### 前置条件

确保已安装以下依赖：

```bash
pip install requests
```

### 安装客户端库

将`brightness_client`文件夹复制到您的项目中，或者将其添加到Python路径。

```python
# 导入客户端
from brightness_client import BrightnessTestClient
```

## 🎯 快速开始

### 基础使用

```python
from brightness_client import BrightnessTestClient

# 创建客户端
client = BrightnessTestClient()

try:
    # 检查服务状态
    if client.is_service_available():
        # 添加内销OLED自动亮度测试
        client.add_内_OLED_自动()
        
        # 启动执行
        client.start_execution()
        
        # 等待完成
        result = client.wait_for_completion()
        print(f"测试完成，状态: {result.summary.execution_status}")
    else:
        print("API服务不可用，请确保主程序已启动")
        
finally:
    client.close()
```

### 使用上下文管理器

```python
with BrightnessTestClient() as client:
    if client.is_service_available():
        client.add_内_OLED_自动()
        client.start_execution()
        result = client.wait_for_completion()
```

## 📋 API参考

### 客户端初始化

```python
client = BrightnessTestClient(
    base_url="http://127.0.0.1:9080",  # API服务器地址
    timeout=30,                        # 请求超时时间（秒）
    max_retries=3,                     # 最大重试次数
    logger=None                        # 自定义日志记录器
)
```

### 基础API方法

#### 服务状态检查

```python
# 健康检查
health = client.health_check()
print(f"服务状态: {health.health}")
print(f"设备连接: {health.device_connected}")

# 检查服务是否可用
is_available = client.is_service_available()

# 获取完整服务信息
info = client.get_service_info()
```

#### 队列管理

```python
# 获取队列状态
queue = client.get_queue()
print(f"队列数量: {queue.queue_count}")

# 启动执行
client.start_execution()

# 停止执行
client.stop_execution()

# 清空队列
client.clear_queue()
```

#### 设备管理

```python
# 获取设备列表
devices = client.get_devices()
print(f"设备数量: {devices.device_count}")
print(f"设备列表: {devices.devices}")
```

#### 预设管理

```python
# 获取所有预设
presets = client.get_presets()
print("5.0版本预设:", presets.get("5.0", []))
print("4.0版本预设:", presets.get("4.0", []))
```

### 5.0版本预设方法

```python
# 内销OLED测试
client.add_内_OLED_自动()    # 内销OLED自动亮度测试
client.add_内_OLED_手动()    # 内销OLED手动亮度测试

# 外销OLED测试
client.add_外_OLED_自动()    # 外销OLED自动亮度测试
client.add_外_OLED_手动()    # 外销OLED手动亮度测试

# 内销LCD测试
client.add_内_LCD_自动()     # 内销LCD自动亮度测试
client.add_内_LCD_手动()     # 内销LCD手动亮度测试

# 外销LCD测试
client.add_外_LCD_自动()     # 外销LCD自动亮度测试
client.add_外_LCD_手动()     # 外销LCD手动亮度测试
```

### 4.0版本预设方法

```python
# 需要指定亮度值列表
brightness_values = [30, 100, 300]

client.add_内销自动(brightness_values)    # 内销自动亮度测试
client.add_内销手动(brightness_values)    # 内销手动亮度测试
client.add_外销自动(brightness_values)    # 外销自动亮度测试
client.add_外销手动(brightness_values)    # 外销手动亮度测试
```

### 任务监控

```python
# 获取执行状态
status = client.get_execution_status()
print(f"执行状态: {status.summary.execution_status}")
print(f"进度: {status.summary.completed_queues}/{status.summary.total_queues}")

# 等待任务完成
result = client.wait_for_completion(timeout=600, poll_interval=5)

# 监控执行状态（带回调）
def status_callback(status):
    print(f"当前状态: {status.summary.execution_status}")

final_status = client.monitor_execution(callback=status_callback)

# 获取执行错误
errors = client.get_execution_errors()
if errors["error_count"] > 0:
    print(f"发现 {errors['error_count']} 个错误")
```

### 批量操作

```python
# 批量添加5.0版本预设
presets = ["内_OLED_自动", "内_OLED_手动", "外_OLED_自动"]
results = client.batch_add_presets_5_0(presets)

# 批量添加4.0版本预设
presets_4_0 = [
    {"preset": "内销自动", "brightness_values": [30, 100, 300]},
    {"preset": "外销自动", "brightness_values": [50, 150, 250]}
]
results = client.batch_add_presets_4_0(presets_4_0)
```

### 自定义队列

```python
# 自定义5.0版本队列
client.add_custom_queue_5_0(
    apps=["微信", "抖音", "王者荣耀"],
    auto_brightness=True,
    preset_name="自定义游戏测试"
)

# 自定义4.0版本队列
client.add_custom_queue_4_0(
    apps=["微信", "QQ"],
    brightness_values=[50, 150, 250],
    auto_brightness=False,
    preset_name="自定义社交应用测试"
)
```

### 便捷方法

```python
# 快速内销OLED测试（自动+手动）
result = client.quick_test_内_OLED(include_manual=True)
print(f"测试完成: {result['execution_status']}")
```

## 🔧 高级功能

### 错误处理

```python
from brightness_client import (
    BrightnessClientError,
    APIConnectionError,
    APIRequestError,
    ServiceUnavailableError,
    ExecutionError
)

try:
    client.add_内_OLED_自动()
except APIConnectionError as e:
    print(f"连接错误: {e}")
except APIRequestError as e:
    print(f"请求错误: {e}, 状态码: {e.status_code}")
except ServiceUnavailableError as e:
    print(f"服务不可用: {e}")
except ExecutionError as e:
    print(f"执行错误: {e}")
except BrightnessClientError as e:
    print(f"客户端错误: {e}")
```

### 自定义日志

```python
import logging

# 创建自定义日志记录器
logger = logging.getLogger('my_app')
logger.setLevel(logging.DEBUG)

# 创建客户端时传入日志记录器
client = BrightnessTestClient(logger=logger)
```

### 配置重试机制

```python
# 配置更长的超时时间和更多重试次数
client = BrightnessTestClient(
    timeout=60,      # 60秒超时
    max_retries=5    # 最多重试5次
)
```

## 📝 数据模型

### HealthStatus

```python
health = client.health_check()
print(health.health)              # 健康状态
print(health.device_connected)    # 设备连接状态
print(health.uptime)              # 运行时间
print(health.is_healthy)          # 是否健康
```

### QueueStatus

```python
queue = client.get_queue()
print(queue.queue_count)          # 队列数量
print(queue.is_running)           # 是否正在运行
print(queue.has_queues)           # 是否有队列
```

### ExecutionStatus

```python
status = client.get_execution_status()
print(status.summary.execution_status)    # 执行状态
print(status.summary.progress_percentage) # 进度百分比
print(status.summary.is_completed)        # 是否完成
```

## 🎯 使用场景

### 第三方应用集成

```python
class MyTestApp:
    def __init__(self):
        self.client = BrightnessTestClient()
    
    def run_brightness_test(self, test_type="内_OLED_自动"):
        """运行亮度测试"""
        try:
            if not self.client.is_service_available():
                return {"success": False, "error": "服务不可用"}
            
            # 清空队列并添加测试
            self.client.clear_queue()
            
            if test_type == "内_OLED_自动":
                self.client.add_内_OLED_自动()
            elif test_type == "内_OLED_手动":
                self.client.add_内_OLED_手动()
            # ... 其他测试类型
            
            # 启动并等待完成
            self.client.start_execution()
            result = self.client.wait_for_completion()
            
            return {
                "success": True,
                "status": result.summary.execution_status,
                "duration": result.summary.duration_seconds
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def close(self):
        self.client.close()
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 确保亮度测试主程序已启动
   - 检查API服务是否运行在9080端口
   - 确认防火墙设置

2. **请求超时**
   - 增加timeout参数值
   - 检查网络连接
   - 确认服务器负载

3. **执行失败**
   - 检查设备连接状态
   - 查看执行错误信息
   - 确认预设配置正确

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查服务信息
info = client.get_service_info()
print(f"服务信息: {info}")

# 检查执行错误
errors = client.get_execution_errors()
if errors["error_count"] > 0:
    print(f"错误详情: {errors}")
```

## 📞 技术支持

如遇到问题，请：

1. 查看客户端日志输出
2. 检查API服务状态
3. 运行示例代码验证功能
4. 参考主程序的API文档

## 📄 许可证

本客户端库与主程序使用相同的许可证。

---

**版本**: 1.0.0  
**兼容性**: Python 3.7+  
**依赖**: requests  
**更新日期**: 2025-07-04
