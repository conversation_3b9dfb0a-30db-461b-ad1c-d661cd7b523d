import os
import time

import config
from utils.Util import Util
from component.GlobalInstanceBase import GlobalInstanceBase
from utils.adb.ExAdb import ExAdb


class ThreeController:
    """ 配置信息 """
    def __init__(self, guiModel, logs):
        self.guiModel = guiModel
        self.global_instance = None
        self.is_auto_brightness = False
        self.adb = ExAdb()
        self.logs = logs
        self.util = Util()

    def set_global_instance(self, global_instance: GlobalInstanceBase):
        self.global_instance = global_instance

    def save_5_config(self, args: dict):
        config_list = self.global_instance.get_instance("ConfigList_5").get_dict_all_data()
        # 循环字典
        for key, value in config_list.items():
            config.update_5_config(key, value)
        print("保存配置信息成功")

    def save_4_config(self, args: dict):
        config_list = self.global_instance.get_instance("ConfigList_4").get_dict_all_data()
        # 循环字典
        for key, value in config_list.items():
            config.update_4_config(key, value)
        print("保存配置信息成功")

    def save_startup_delays_5(self, args: dict):
        """保存5.0版本的应用启动延时配置"""
        try:
            # 获取输入值
            init_delay_str = args.get("InitDelayInput", {}).get() if args.get("InitDelayInput") else "10"
            brightness_delay_str = args.get("BrightnessDelayInput", {}).get() if args.get("BrightnessDelayInput") else "10"

            # 验证输入值
            try:
                init_delay = int(init_delay_str)
                brightness_delay = int(brightness_delay_str)
            except ValueError:
                print("错误：延时时间必须是整数")
                return

            # 验证范围
            if not (5 <= init_delay <= 30):
                print("错误：初始化等待时间必须在5-30秒之间")
                return
            if not (5 <= brightness_delay <= 30):
                print("错误：亮度稳定等待时间必须在5-30秒之间")
                return

            # 保存配置
            config.update_app_startup_delays_5(
                init_delay=init_delay,
                brightness_stabilize_delay=brightness_delay
            )
            print(f"保存延时配置成功：初始化等待{init_delay}秒，亮度稳定等待{brightness_delay}秒")

        except Exception as e:
            print(f"保存延时配置失败：{e}")

    def save_startup_delays_4(self, args: dict):
        """保存4.0版本的应用启动延时配置"""
        try:
            # 获取输入值
            init_delay_str = args.get("InitDelayInputFour", {}).get() if args.get("InitDelayInputFour") else "10"
            brightness_delay_str = args.get("BrightnessDelayInputFour", {}).get() if args.get("BrightnessDelayInputFour") else "10"

            # 验证输入值
            try:
                init_delay = int(init_delay_str)
                brightness_delay = int(brightness_delay_str)
            except ValueError:
                print("错误：延时时间必须是整数")
                return

            # 验证范围
            if not (5 <= init_delay <= 30):
                print("错误：初始化等待时间必须在5-30秒之间")
                return
            if not (5 <= brightness_delay <= 30):
                print("错误：亮度稳定等待时间必须在5-30秒之间")
                return

            # 保存配置
            config.update_app_startup_delays_4(
                init_delay=init_delay,
                brightness_stabilize_delay=brightness_delay
            )
            print(f"保存延时配置成功：初始化等待{init_delay}秒，亮度稳定等待{brightness_delay}秒")

        except Exception as e:
            print(f"保存延时配置失败：{e}")
