import os
import time

import config
from utils.Util import Util
from component.GlobalInstanceBase import GlobalInstanceBase
from utils.adb.ExAdb import ExAdb


class ThreeController:
    """ 配置信息 """
    def __init__(self, guiModel, logs):
        self.guiModel = guiModel
        self.global_instance = None
        self.is_auto_brightness = False
        self.adb = ExAdb()
        self.logs = logs
        self.util = Util()

    def set_global_instance(self, global_instance: GlobalInstanceBase):
        self.global_instance = global_instance

    def save_5_config(self, args: dict):
        config_list = self.global_instance.get_instance("ConfigList_5").get_dict_all_data()
        # 循环字典
        for key, value in config_list.items():
            config.update_5_config(key, value)
        print("保存配置信息成功")

    def save_4_config(self, args: dict):
        config_list = self.global_instance.get_instance("ConfigList_4").get_dict_all_data()
        # 循环字典
        for key, value in config_list.items():
            config.update_4_config(key, value)
        print("保存配置信息成功")
