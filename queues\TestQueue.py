import datetime
import math
import threading
import time

import config
from enums.PackageName import PackageName
from utils import ExU2
from utils.adb.OpenApp import OpenApp
from utils.adb.ExAdb import ExAdb
from utils.GetBrightnessReduction import GetBrightnessReduction
from utils.ExcelSave import ExcelSave
from utils.AppConfig import AppConfig
from utils.PresetsConfig import PresetsConfig
from utils.PopupMonitor import PopupMonitor


class TestQueue:
    """ 测试队列 """
    def __init__(self):
        self.queue = []
        self.queue_title = None
        self.brightnessModel = GetBrightnessReduction()
        self.adb = ExAdb()
        # 创建AppConfig实例
        self.app_config = AppConfig(config_path="app_config.json", version="5.0")
        # 兼容旧版本，保留package_dict和package_key
        self.package_dict = {item.value.get("desc"): item.value.get("package") for item in PackageName}
        self.package_key = {item.value.get("desc"): item.name for item in PackageName}
        self.config_dict = config.get_5_config()
        self.test_standard = "indoor"  # 测试标准 indoor（室内）/outdoor（室外）
        self.res_dict = {}
        self.is_auto_brightness = False
        self.file_info = "A"
        self.logs = None
        self.is_start_brightness_thread = False
        self.file_name = None
        # 进度条组件实例
        self.progress_bar = None
        # 创建OpenApp实例，用于获取包名
        self.open_app = OpenApp()

    def init_app(self):
        """ 初始化 """
        self.adb.get_root()
        time.sleep(5)

    def start_brightness_thread(self):
        """ 启动亮度线程 """
        self.brightnessModel.start_threads()
        self.is_start_brightness_thread = True

    def stop_brightness_thread(self):
        """ 停止亮度线程 """
        self.brightnessModel.stop_threads()
        self.is_start_brightness_thread = False

    def get_test_standard(self):
        return self.test_standard

    def set_test_standard(self, value):
        self.test_standard = value

    def get_file_name(self):
        return self.file_name

    def get_new_file_name(self, device_id, formatted_datetime):
        """ 获取新的文件名称 """
        # 获取设备列表
        device_list = self.adb.get_device_list()
        
        # 如果没有设备或device_id为None，则使用默认文件名
        if not device_list or device_id is None:
            return str(formatted_datetime) + "_" + self.get_queue_title() + ".xlsx"
        
        # 如果只有一个设备，直接使用该设备名称
        if len(device_list) == 1:
            device_name = self.get_device_name(device_list[0])
            return str(device_name) + "_" + str(formatted_datetime) + "_" + self.get_queue_title() + ".xlsx"
        
        # 如果有多个设备，使用第一个设备名称，并在文件名中标注多设备
        device_name = self.get_device_name(device_list[0])
        return str(device_name) + "_Multi_" + str(formatted_datetime) + "_" + self.get_queue_title() + ".xlsx"

    def set_file_name(self, file_name):
        self.file_name = file_name

    def get_config_dict(self):
        return self.config_dict

    def set_config_dict(self, config_dict):
        self.config_dict = config_dict

    def get_current_time(self):
        # 获取当前时间
        now = datetime.datetime.now()
        formatted_datetime = now.strftime('%Y-%m-%d %H-%M-%S')
        return formatted_datetime

    def get_is_start_brightness_thread(self):
        """ 获取亮度线程状态 """
        return self.is_start_brightness_thread

    def set_logs(self, logs):
        self.logs = logs
        # 设置OpenApp实例的logs
        self.open_app.logs = logs

    def set_file_info(self, file_info):
        self.file_info = file_info

    def get_queue_title(self):
        """ 获取队列标题 """
        if not self.queue_title:
            self.queue_title = "测试结果"
        return self.queue_title

    def set_queue_title(self, title):
        """ 设置队列标题 """
        self.queue_title = title

    def set_progress_bar(self, progress_bar):
        """ 设置进度条 """
        self.progress_bar = progress_bar

    def set_is_auto_brightness(self, is_auto_brightness):
        """ 设置是否自动亮度 """
        self.is_auto_brightness = is_auto_brightness

    def get_is_auto_brightness(self):
        """ 获取是否自动亮度 """
        return self.is_auto_brightness

    def get_device_name(self, device_id):
        """ 获取设备名称 """
        device_name = self.adb.get_device_name(device_id)
        cleaned_device_name = device_name.replace('\n', '').replace('\r', '')
        return cleaned_device_name

    def get_nit_value(self, device_id):
        """ 获取nit值 """
        return self.brightnessModel.get_current_nit_value(device_id)

    def get_absolute_nit_value(self, device_id):
        """ 获取nit值 不受降幅影响的nit值 """
        return self.brightnessModel.get_current_absolute_nit_value(device_id=device_id)

    def get_drop_value(self, device_id):
        """ 获取亮度降幅 """
        return self.brightnessModel.get_current_brightness_drop(device_id)

    def set_queue_data(self, data):
        """ 设置队列数据 """
        self.queue = data

    def set_schedule(self, current=0, target=10):
        """ 设置进度条进度 """
        if current > 100:
            current = 100
        for i in range(current, target, 1):
            time.sleep(0.1)
            self.progress_bar.set(current)

    def calculate_task_length(self):
        """ 计算任务长度 """
        num = 100 / len(self.queue)
        result = math.ceil(num)
        return result

    def device_init(self):
        """ 设备初始化 """
        # 获取root权限
        self.adb.get_root()
        time.sleep(3)
        # 唤醒屏幕
        self.adb.wake_up_screen()
        time.sleep(3)
        # 自动亮度
        if self.is_auto_brightness:
            self.adb.start_auto_brightness()
        else:
            self.adb.close_auto_brightness()
        # 等待亮度稳定
        time.sleep(5)
        # 最大亮度
        self.adb.switch_brightness_255(255)
        time.sleep(3)

    def get_expected_package_name(self, desc):
        """
        根据场景描述获取预期的应用包名

        Args:
            desc (str): 应用场景描述

        Returns:
            str: 预期的应用包名
        """
        # 对特殊场景进行处理
        if desc == "桌面" or desc == "系统桌面":
            return "系统桌面"
        elif desc == "负一屏":
            return "负一屏"

        # 优先使用AppConfig获取包名
        if self.app_config:
            package_name = self.app_config.get_app_package(desc)
            if package_name:
                return package_name

        # 兼容旧版本，使用OpenApp的get_package_name方法
        package_name = self.open_app.get_package_name(desc)
        return package_name

    def get_actual_package_name(self, device_id=None):
        """
        获取当前设备实际运行的应用包名

        Args:
            device_id (str, optional): 设备ID

        Returns:
            str: 当前实际运行的应用包名，获取失败时返回None
        """
        try:
            # 使用adb实例获取当前运行的包名，支持指定设备ID
            current_package = self.adb.get_activity_info(device_id)

            if current_package and self.logs:
                self.logs.info(f"设备 {device_id or 'default'} 当前实际运行的包名: {current_package}")

            return current_package

        except Exception as e:
            if self.logs:
                self.logs.error(f"获取设备 {device_id or 'default'} 实际包名失败: {e}")
            return None

    def start_queue(self, device_id, sheet_name=None, add_separator=False, is_headers=False):
        """ 开始测试队列 """
        print("初始化设备"+device_id)
        self.logs.info("初始化设备"+device_id)
        self.device_init()
        open_app = OpenApp()
        open_app.set_device_id(device_id)
        # 设置应用配置
        open_app.set_app_config(self.app_config)

        # 启用数据记录功能
        if hasattr(self, 'brightnessModel') and self.brightnessModel:
            base_filename = self.get_file_name()
            if base_filename:
                # 移除.xlsx扩展名，使用基础文件名
                base_filename = base_filename.replace('.xlsx', '')
            self.brightnessModel.enable_data_logging(base_filename)
        
        # 为当前设备/线程创建并启动独立的弹窗监控
        popup_monitor = PopupMonitor(device_id=device_id)
        popup_monitor.set_logs(self.logs)
        self.logs.info(f"为设备 {device_id} 启动弹窗监控")
        popup_monitor.start_monitor()
        
        # 使用sheet_name参数创建ExcelSave实例
        if self.get_file_name() is None:
            # 生成基础文件名，并设置到TestQueue实例中，以便后续测试使用
            base_file_name = self.get_new_file_name(device_id, self.get_current_time())
            self.set_file_name(base_file_name)
            # 创建ExcelSave实例
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name=sheet_name)
        else:
            # 使用现有文件名
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name=sheet_name)
        # 创建ExU2实例
        u2_helper = ExU2(device_id=device_id)
        data = {}
        length_value = self.calculate_task_length()
        current_value = 0
        for desc in self.queue:
            print(f"设备Id:{device_id}, 当前场景:{desc}")
            self.logs.info(f"设备Id:{device_id}, 当前场景:{desc}")

            # 更新当前场景信息到亮度数据记录器
            if hasattr(self, 'brightnessModel') and self.brightnessModel:
                self.brightnessModel.set_current_scene(desc)

            # 使用AppConfig获取应用的打开方法
            open_method = self.app_config.get_open_method(desc)
            
            # 根据打开方法选择不同的处理逻辑
            if open_method == "special":
                # 特殊应用需要使用特定的打开方法
                if desc == "桌面" or desc == "系统桌面":
                    open_app.open_desktop()
                elif desc == "负一屏":
                    open_app.open_negative_one_screen()
                elif desc == "微信视频":
                    open_app.open_weixin_video()
                else:
                    # 如果没有特殊处理方法，使用通用方法打开
                    open_app.open_app(desc)
            else:
                # 使用通用方法打开应用
                open_app.open_app(desc)
            
            # 应用打开后等待片刻，让弹窗监控处理可能出现的弹窗（如果已设置）
            if popup_monitor:
                self.logs.info(f"应用 {desc} 打开后等待弹窗处理")
                time.sleep(1)
            # 调整亮度
            self.adb.switch_brightness_255(200, device_id)
            time.sleep(3)

            # 获取预期的包名和实际的包名
            expected_package = self.get_expected_package_name(desc)
            actual_package = self.get_actual_package_name(device_id)

            # 验证场景是否匹配
            scene_match = False

            # 首先判断是否为特殊场景
            if expected_package in ["系统桌面", "负一屏"]:
                scene_match = True
                self.logs.info(f"特殊场景 {desc} 已处理，预期标识: {expected_package}")
            elif expected_package and actual_package:
                # 普通场景的包名匹配验证
                if expected_package == actual_package:
                    scene_match = True
                    self.logs.info(f"场景 {desc} 正确打开，包名匹配: {expected_package}")
                else:
                    scene_match = False
                    self.logs.warning(f"场景 {desc} 不匹配！预期包名: {expected_package}, 实际包名: {actual_package}")
                    self.logs.warning(f"设备 {device_id} 当前可能未正确打开场景 '{desc}'")
            elif expected_package:
                # 有预期包名但无法获取实际包名，记录警告但不影响测试
                scene_match = True  # 默认认为匹配，避免误报
                self.logs.warning(f"场景 {desc} 无法验证包名匹配性，预期包名: {expected_package}, 实际包名获取失败")
            else:
                # 无法获取预期包名，记录错误
                scene_match = False
                self.logs.error(f"场景 {desc} 配置错误，无法获取预期包名")

            if u2_helper.contains_text("选择一个账号登录"):
                u2_helper.perform_action_on_mismatch()
                time.sleep(3)
            if u2_helper.contains_text("密码管理工具"):
                u2_helper.perform_action_on_mismatch(offset_y=600)

            # 最大亮度
            self.adb.switch_brightness_255(255, device_id)
            time.sleep(5)
            self.set_schedule(current_value, current_value+length_value)
            current_value += length_value
            current_nit_value = self.get_nit_value(device_id)
            drop_value = self.get_drop_value(device_id)
            nit_absolute_value = self.get_absolute_nit_value(device_id)
            check_value, standard_value = self.is_test_result(current_nit_value=current_nit_value, desc=desc,nit_absolute_value=nit_absolute_value, drop_current_value=drop_value)

            # 根据场景匹配状态生成data_key
            if scene_match:
                data_key = f"{desc}"
                self.logs.info(f"场景 {desc} 测试数据记录: nit={current_nit_value}, drop={self.get_drop_value(device_id)}, result={check_value}")
            else:
                data_key = f"{desc}[场景不匹配]"
                self.logs.warning(f"场景 {desc}[场景不匹配] 测试数据记录: nit={current_nit_value}, drop={self.get_drop_value(device_id)}, result={check_value}")
                self.logs.warning(f"注意：此数据可能不准确，因为实际打开的应用与预期不符")

            data[data_key] = {"nit": current_nit_value, "drop": self.get_drop_value(device_id), "standard": standard_value,
                          "result": check_value}
            if "微信视频" in desc:
                # 关闭当前app
                self.adb.stop_current_app(device_id)

        # 持久化数据，但不添加元数据（is_final=False），留到所有测试完成后统一添加
        excel_model.append_dict_to_excel(data=data, current_nit='MAX', is_headers=is_headers, add_separator=add_separator, start_row=5)
        
        # 测试结束后，停止当前设备的弹窗监控
        self.logs.info(f"为设备 {device_id} 停止弹窗监控")
        popup_monitor.stop_monitor()
        
        # 保存文件
        excel_model.save(self.get_file_name())

    def start_test_threads(self, sheet_name=None, add_separator=False, is_headers=False):
        """启动测试线程，但不管理亮度线程，亮度线程由测试方法管理"""
        if not self.is_start_brightness_thread:
            return
        device_list = self.adb.get_device_list()
        self.logs.info(f"启动线程的设备列表:{device_list}")
        threads = []
        
        # 创建并启动线程
        for device_id in device_list:
            thread = threading.Thread(target=self.start_queue, args=(device_id,), kwargs={"sheet_name": sheet_name, "add_separator": add_separator, "is_headers": is_headers})
            thread.daemon = True
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        self.logs.info(f"结束线程的设备列表:{device_list}")

    def auto_and_manual_test(self):
        """ 自动和手动亮度降幅测试 """
        # 启动亮度线程
        self.start_brightness_thread()
        
        # 重置文件名，确保每次测试生成新文件
        self.set_file_name(None)
        device_list = self.adb.get_device_list()
        if len(device_list) < 1:
            return
        # 生成一个基础文件名，用于整个测试过程
        base_file_name = self.get_new_file_name(device_list[0], self.get_current_time())
        self.set_file_name(base_file_name)
        
        # 从预设配置中获取应用列表
        presets_config = PresetsConfig(version="5.0")
        
        # 1. 自动亮度测试
        # 从预设配置文件中获取应用列表
        preset_auto_list = presets_config.get_preset_apps("auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_auto_list:
            auto_list = preset_auto_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            auto_list_dict = {}
            for item in PackageName.get_package_auto_list():
                app_desc = item.value.get("desc")
                auto_list_dict[app_desc] = True
            auto_list = list(auto_list_dict.keys())
        
        # 设置队列数据并执行测试
        self.set_queue_data(auto_list)
        self.set_is_auto_brightness(True)
        self.set_file_info("自动亮度")
        self.start_test_threads(sheet_name="自动亮度", add_separator=True, is_headers=False)
        
        # 在自动亮度测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="自动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="自动亮度")
            # 保存文件后立即关闭，避免持续占用
            excel_model.save(self.get_file_name())
            break
        
        # 2. 手动亮度测试
        # 从预设配置文件中获取应用列表
        preset_manual_list = presets_config.get_preset_apps("auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_manual_list:
            manual_list = preset_manual_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            manual_list_dict = {}
            for item in PackageName.get_package_manua_list():
                app_desc = item.value.get("desc")
                manual_list_dict[app_desc] = True
            manual_list = list(manual_list_dict.keys())
        
        # 设置队列数据并执行测试
        self.set_queue_data(manual_list)
        self.set_is_auto_brightness(False)
        self.set_file_info("手动亮度")
        self.start_test_threads(sheet_name="手动亮度", add_separator=True, is_headers=False)
        
        # 在手动亮度测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="手动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="手动亮度")
            # 保存文件并关闭
            excel_model.save(self.get_file_name())
            break
            
        # 停止亮度线程
        self.stop_brightness_thread()

    def ex_auto_and_manual_test(self):
        """ 外销自动和手动亮度 """
        # 启动亮度线程
        self.start_brightness_thread()
        
        # 重置文件名，确保每次测试生成新文件
        self.set_file_name(None)
        device_list = self.adb.get_device_list()
        if len(device_list) < 1:
            return
        # 生成一个基础文件名，用于整个测试过程
        base_file_name = self.get_new_file_name(device_list[0], self.get_current_time())
        self.set_file_name(base_file_name)
        
        # 从预设配置中获取应用列表
        presets_config = PresetsConfig(version="5.0")
        
        # 1. 外销自动亮度测试
        # 从预设配置文件中获取应用列表
        preset_ex_auto_list = presets_config.get_preset_apps("ex_auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_ex_auto_list:
            ex_auto_list = preset_ex_auto_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            ex_auto_list_dict = {}
            for item in PackageName.get_package_ex_auto_list():
                app_desc = item.value.get("desc")
                ex_auto_list_dict[app_desc] = True
            ex_auto_list = list(ex_auto_list_dict.keys())
        
        # 设置队列数据并执行测试
        self.set_queue_data(ex_auto_list)
        self.set_is_auto_brightness(True)
        self.set_file_info("外销自动亮度")
        self.start_test_threads(sheet_name="外销自动亮度", add_separator=True, is_headers=False)
        
        # 在外销自动亮度测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="外销自动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="外销自动亮度")
            # 保存文件后立即关闭，避免持续占用
            excel_model.save(self.get_file_name())
            break
        
        # 2. 外销手动亮度测试
        # 从预设配置文件中获取应用列表
        preset_ex_manual_list = presets_config.get_preset_apps("ex_auto_manual")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_ex_manual_list:
            ex_manual_list = preset_ex_manual_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            ex_manual_list_dict = {}
            for item in PackageName.get_package_ex_manua_list():
                app_desc = item.value.get("desc")
                ex_manual_list_dict[app_desc] = True
            ex_manual_list = list(ex_manual_list_dict.keys())
        
        # 设置队列数据并执行测试
        self.set_queue_data(ex_manual_list)
        self.set_is_auto_brightness(False)
        self.set_file_info("外销手动亮度")
        self.start_test_threads(sheet_name="外销手动亮度", add_separator=True, is_headers=False)
        
        # 在外销手动亮度测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="外销手动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="外销手动亮度")
            # 保存文件并关闭
            excel_model.save(self.get_file_name())
            break
            
        # 停止亮度线程
        self.stop_brightness_thread()

    def outdoor_auto_and_manual_test(self):
        """ 户外自动和手动亮度 """
        # 启动亮度线程
        self.start_brightness_thread()
        
        # 重置文件名，确保每次测试生成新文件
        self.set_file_name(None)
        device_list = self.adb.get_device_list()
        if len(device_list) < 1:
            return
        # 生成一个基础文件名，用于整个测试过程
        base_file_name = self.get_new_file_name(device_list[0], self.get_current_time())
        self.set_file_name(base_file_name)
        
        # 从预设配置中获取应用列表
        presets_config = PresetsConfig(version="5.0")
        
        # 从预设配置文件中获取应用列表
        preset_outdoor_list = presets_config.get_preset_apps("outdoor")
        
        # 使用从预设配置中获取的应用列表，不再合并其他列表
        if preset_outdoor_list:
            outdoor_list = preset_outdoor_list
        else:
            # 只有在预设配置为空时才使用旧版本的列表
            outdoor_list_dict = {}
            for item in PackageName.get_package_outdoor_auto_and_manua_list():
                app_desc = item.value.get("desc")
                outdoor_list_dict[app_desc] = True
            outdoor_list = list(outdoor_list_dict.keys())
        
        # 自动亮度测试
        self.set_queue_data(outdoor_list)
        self.set_is_auto_brightness(True)
        self.set_file_info("户外自动亮度")
        self.start_test_threads(sheet_name="户外自动亮度", add_separator=True, is_headers=False)
        
        # 在户外自动亮度测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="户外自动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="户外自动亮度")
            # 保存文件后立即关闭，避免持续占用
            excel_model.save(self.get_file_name())
            break
        
        # 手动亮度测试
        self.set_queue_data(outdoor_list)
        self.set_is_auto_brightness(False)
        self.set_file_info("户外手动亮度")
        self.start_test_threads(sheet_name="户外手动亮度", add_separator=True, is_headers=False)
        
        # 在户外手动亮度测试完成后，创建一个新的ExcelSave实例来添加元数据
        # 使用append_mode=True确保不会覆盖已有数据
        for device_id in device_list:
            excel_model = ExcelSave(device_id=device_id, filename=self.get_file_name(), sheet_name="户外手动亮度", append_mode=True)
            excel_model.add_metadata_to_sheet(sheet_name="户外手动亮度")
            # 保存文件并关闭
            excel_model.save(self.get_file_name())
            break
            
        # 停止亮度线程
        self.stop_brightness_thread()

    def is_test_result(self, current_nit_value, desc, nit_absolute_value, drop_current_value):
        """ 判断测试结果 return bool(是否符合标准) string(标准值) """
        # 优先使用AppConfig获取标准值
        key = self.app_config.get_key_by_desc(desc)
        if key:
            # 使用AppConfig获取标准值
            is_indoor = self.get_test_standard() == "indoor"
            is_auto = self.get_is_auto_brightness()
            drop_value = self.app_config.get_standard_value(desc, is_indoor, is_auto)
        else:
            # 兼容旧版本
            if desc not in self.package_key:
                return False, "0"
            
            key = self.package_key[desc]
            config_dict = self.get_config_dict()
            config_data = config_dict.get(key)
            # 降幅标准值 indoor（室内）/ outdoor（室外）
            if self.get_test_standard() == "indoor":
                drop_value = config_data.get("indoor")
            else:
                drop_value = config_data.get("outdoor")

            if self.get_is_auto_brightness():
                # 自动亮度
                drop_value = drop_value.split("/")[0]
            else:
                # 手动亮度
                drop_value = drop_value.split("/")[1]

        if "%" in drop_value:
            drop_value = drop_value.split("%")[0]
            # 降幅为0，直接判断当前亮度值是否等于绝对亮度值
            if float(drop_value) < 1:
                return float(drop_current_value) < 1, drop_value

            # 降幅不为0，判断当前亮度值是否在标准值的范围内
            if float(drop_value) - 3 <= float(drop_current_value) <= float(drop_value) + 3:
                return True, drop_value
        else:
            drop_value = drop_value.split("nit")[0]
            # 降幅为0，直接判断当前亮度值是否等于绝对亮度值
            if int(drop_value) == 0:
                return current_nit_value == nit_absolute_value, drop_value

            # 降幅不为0，判断当前亮度值是否在标准值的范围内
            if int(drop_value) - 5 <= int(current_nit_value) <= int(drop_value) + 5:
                return True, drop_value

        return False, drop_value

    def set_version(self, version):
        """设置标准版本 4.0 或 5.0"""
        self.app_config.set_version(version)

    def set_nit_arr_int(self, nit_arr_int):
        """
        设置测试亮度数组（4.0版本需要）
        :param nit_arr_int: 亮度值数组
        """
        self.nit_arr_int = nit_arr_int
        
    def get_nit_arr_int(self):
        """
        获取测试亮度数组（4.0版本需要）
        :return: 亮度值数组
        """
        return getattr(self, 'nit_arr_int', None)
