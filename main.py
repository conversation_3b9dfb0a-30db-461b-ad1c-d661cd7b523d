import time
from utils.logs.LoggerManager import init_logging, get_logger

import encodings
import os
import sys
import tkinter
import subprocess
from tkinter import ttk
from qgui import CreateQG<PERSON>, MessageBox
from qgui.banner_tools import BaseBarTool, GitHub, AIStudio
from qgui.notebook_tools import *
from qgui.manager import Q<PERSON>tyle, HOR<PERSON>ZONTAL

from component.GlobalInstanceBase import GlobalInstanceBase
from controller.MainController import MainController
from controller.FourController import FourController
from controller.ThreeController import ThreeController
from controller.QueueController import QueueController
from enums.PackageName import PackageName
from utils.AppConfig import AppConfig
from utils.PresetsConfig import PresetsConfig
import config

# 导入API服务
try:
    from api.api_server import APIServer
    API_AVAILABLE = True
except ImportError as e:
    print(f"API服务不可用: {e}")
    API_AVAILABLE = False

# 打开程序运行文件夹
def open_program_folder(args):
    """打开测试结果文件所在的文件夹"""
    try:
        current_path = os.path.abspath(os.path.dirname(__file__))
        if os.name == 'nt':  # Windows系统
            os.startfile(current_path)
        elif os.name == 'posix':  # macOS或Linux系统
            subprocess.call(['open', current_path] if sys.platform == 'darwin' else ['xdg-open', current_path])
        logger.info(f"打开测试结果文件夹: {current_path}")
    except Exception as e:
        logger.error(f"打开文件夹失败: {str(e)}")
        MessageBox.error(f"打开文件夹失败: {str(e)}")

# 初始化AppConfig
app_config = AppConfig(config_path="app_config.json", version="5.0")

# 获取应用列表
def get_app_list():
    """
    获取应用列表，合并旧逻辑和新逻辑获取的应用列表，以新逻辑为主
    """
    # 从旧逻辑获取应用列表
    old_app_list = PackageName.get_tuples()
    old_app_dict = {item[0]: item[1] for item in old_app_list}
    
    # 从新逻辑获取应用列表
    new_app_list = []
    for key, app_info in app_config.app_config.items():
        desc = app_info.get("desc", "")
        if desc:
            new_app_list.append((desc, 0))
    
    # 合并应用列表，以新逻辑为主
    merged_app_dict = old_app_dict.copy()
    for app in new_app_list:
        merged_app_dict[app[0]] = app[1]
    
    # 转换回列表格式
    merged_app_list = [(desc, value) for desc, value in merged_app_dict.items()]
    
    return merged_app_list

# 获取应用列表
package_list = get_app_list()

# 日志初始化
init_logging()
logger = get_logger(__name__)

# 创建主界面
q_gui = CreateQGUI(title="显示亮度降幅自动化",  # 界面标题
                   # tab_names=["亮度降幅5.0", "亮度降幅4.0", "配置信息5.0", "配置信息4.0"],  # 界面中心部分的分页标题 - 可不填
                   tab_names=["亮度降幅5.0", "亮度降幅4.0", "配置信息5.0", "预设配置", "任务队列"],  # 界面中心部分的分页标题
                   style=QStyle.default)  # 皮肤
# (亮度降幅5.0)主控制器
controller = MainController(guiModel=q_gui, logs=logger)
# (亮度降幅4.0)控制器
controller_four = FourController(guiModel=q_gui, logs=logger)
# (配置信息)控制器
controller_three = ThreeController(guiModel=q_gui, logs=logger)
# (队列)控制器
controller_queue = QueueController(guiModel=q_gui, logs=logger)

tab_index_5 = 0  # 亮度降幅5.0
tab_index_4 = 1  # 亮度降幅4.0
tab_index_5_config = 2  # 配置信息5.0
tab_index_presets = 3  # 预设配置
tab_index_queue = 4  # 任务队列

# 全局实例
globalModel = GlobalInstanceBase()
# 第一页的实例
controller.set_global_instance(globalModel)
TaskTool = CustomTaskQueueDisplay(name="QueueDisplay", bind_func=controller.queue_click, tab_index=tab_index_5, max_width=200)
CheckObviousToolLine = CheckObviousToolButtonAutoLine(name="CheckObviousToolLine", options=package_list, title=" ", line_width=550, bind_func=controller.add_queue_sub, tab_index=tab_index_5)
AutoBrightnessSwitch = ToggleButton(options=("自动亮度", 0), title="", name="auto_brightness_switch", bind_func=controller.auto_brightness_switch, tab_index=tab_index_5)
# 将"加入执行队列"按钮提前定义，以便后续直接加入 VerticalFrameCombine
add_to_queue_button_5 = BaseButton(
    bind_func=controller.add_to_execution_queue,
    text="加入执行队列",
    tab_index=tab_index_5,
    side=LEFT
)

globalModel.add_instance(TaskTool)
globalModel.add_instance(CheckObviousToolLine)
globalModel.add_instance(AutoBrightnessSwitch)

# 第二页的实例
controller_four.set_global_instance(globalModel)
TaskToolFour = CustomTaskQueueDisplay(name="QueueDisplayFour", bind_func=controller_four.queue_click, tab_index=tab_index_4, max_width=350)
# 亮度降幅4.0 加入执行队列按钮提前定义，供后续 VerticalFrameCombine 使用
add_to_queue_button_4 = BaseButton(
    bind_func=controller_four.add_to_execution_queue,
    text="加入执行队列",
    tab_index=tab_index_4,
    side=LEFT
)
CheckObviousToolLineFour = CheckObviousToolButtonAutoLine(name="CheckObviousToolLineFour", options=package_list, title=" ", line_width=550, bind_func=controller_four.add_queue_sub, tab_index=tab_index_4)
AutoBrightnessSwitchFour = ToggleButton(options=("自动亮度", 0), title="", name="AutoBrightnessSwitchFour", bind_func=controller_four.auto_brightness_switch, tab_index=tab_index_4)
globalModel.add_instance(TaskToolFour)
globalModel.add_instance(CheckObviousToolLineFour)
globalModel.add_instance(AutoBrightnessSwitchFour)

# 第三页的实例
controller_three.set_global_instance(globalModel)
config_5_dict = config.get_5_config()
list_5_component = ListComponent_5(name="ConfigList_5", items=config_5_dict, title="", tab_index=tab_index_5_config, height=15)
globalModel.add_instance(list_5_component)

# 第四页的实例 - 预设配置
# 创建预设配置页面
presets_config = PresetsConfig()
presets_5_0 = presets_config.get_all_presets()
presets_config.set_version("4.0")
presets_4_0 = presets_config.get_all_presets()

# 创建带参数的包装函数
def create_save_preset_func(controller_obj, preset_key):
    return lambda args: controller_obj.save_preset(preset_key)

# 创建恢复默认预设的函数
def restore_default_preset(version, preset_key=None):
    def restore_func(args):
        preset_config = PresetsConfig(version=version)
        success = False
        
        if preset_key:
            success = preset_config.restore_default_preset(preset_key)
            if success:
                MessageBox.info(f"恢复{version}版本的{preset_config.get_preset_name(preset_key)}预设成功")
            else:
                MessageBox.info(f"恢复{version}版本的预设失败")
        else:
            success = preset_config.restore_default_preset()
            if success:
                MessageBox.info(f"恢复{version}版本的所有预设成功")
            else:
                MessageBox.info(f"恢复{version}版本的所有预设失败")
        
        # 重新加载控制器预设配置
        if success:
            if version == "5.0":
                controller.reload_presets()
            elif version == "4.0":
                controller_four.reload_presets()
    return restore_func

def restore_all_default_presets(args):
    preset_config = PresetsConfig()
    success = preset_config.restore_all_default_presets()
    if success:
        MessageBox.info("恢复所有版本的预设成功")
        # 重新加载两个控制器的预设配置
        controller.reload_presets()
        controller_four.reload_presets()
    else:
        MessageBox.info("恢复所有版本的预设失败")

# 5.0版本预设保存按钮
save_5_0_oled_in_auto_button = BaseButton(
    bind_func=create_save_preset_func(controller, "内_OLED_自动"),
    text="保存内_OLED_自动预设",
    tab_index=tab_index_presets
)
save_5_0_oled_in_manual_button = BaseButton(
    bind_func=create_save_preset_func(controller, "内_OLED_手动"),
    text="保存内_OLED_手动预设",
    tab_index=tab_index_presets
)
save_5_0_oled_ex_auto_button = BaseButton(
    bind_func=create_save_preset_func(controller, "外_OLED_自动"),
    text="保存外_OLED_自动预设",
    tab_index=tab_index_presets
)
save_5_0_oled_ex_manual_button = BaseButton(
    bind_func=create_save_preset_func(controller, "外_OLED_手动"),
    text="保存外_OLED_手动预设",
    tab_index=tab_index_presets
)
save_5_0_lcd_in_auto_button = BaseButton(
    bind_func=create_save_preset_func(controller, "内_LCD_自动"),
    text="保存内_LCD_自动预设",
    tab_index=tab_index_presets
)
save_5_0_lcd_in_manual_button = BaseButton(
    bind_func=create_save_preset_func(controller, "内_LCD_手动"),
    text="保存内_LCD_手动预设",
    tab_index=tab_index_presets
)
save_5_0_lcd_ex_auto_button = BaseButton(
    bind_func=create_save_preset_func(controller, "外_LCD_自动"),
    text="保存外_LCD_自动预设",
    tab_index=tab_index_presets
)
save_5_0_lcd_ex_manual_button = BaseButton(
    bind_func=create_save_preset_func(controller, "外_LCD_手动"),
    text="保存外_LCD_手动预设",
    tab_index=tab_index_presets
)

# 4.0版本预设保存按钮
save_4_0_auto_button = BaseButton(
    bind_func=create_save_preset_func(controller_four, "auto_manual"),
    text="保存4.0自/手动亮度预设",
    tab_index=tab_index_presets
)
save_4_0_manual_button = BaseButton(
    bind_func=create_save_preset_func(controller_four, "manual"),
    text="保存4.0手动亮度预设",
    tab_index=tab_index_presets
)
save_4_0_ex_auto_button = BaseButton(
    bind_func=create_save_preset_func(controller_four, "ex_auto_manual"),
    text="保存4.0外销自/手动亮度预设",
    tab_index=tab_index_presets
)
save_4_0_ex_manual_button = BaseButton(
    bind_func=create_save_preset_func(controller_four, "ex_manual"),
    text="保存4.0外销手动亮度预设",
    tab_index=tab_index_presets
)

# 5.0版本预设恢复按钮
restore_5_0_oled_in_auto_button = BaseButton(
    bind_func=restore_default_preset("5.0", "内_OLED_自动"),
    text="恢复内_OLED_自动预设",
    tab_index=tab_index_presets
)
restore_5_0_oled_in_manual_button = BaseButton(
    bind_func=restore_default_preset("5.0", "内_OLED_手动"),
    text="恢复内_OLED_手动预设",
    tab_index=tab_index_presets
)
restore_5_0_oled_ex_auto_button = BaseButton(
    bind_func=restore_default_preset("5.0", "外_OLED_自动"),
    text="恢复外_OLED_自动预设",
    tab_index=tab_index_presets
)
restore_5_0_oled_ex_manual_button = BaseButton(
    bind_func=restore_default_preset("5.0", "外_OLED_手动"),
    text="恢复外_OLED_手动预设",
    tab_index=tab_index_presets
)
restore_5_0_lcd_in_auto_button = BaseButton(
    bind_func=restore_default_preset("5.0", "内_LCD_自动"),
    text="恢复内_LCD_自动预设",
    tab_index=tab_index_presets
)
restore_5_0_lcd_in_manual_button = BaseButton(
    bind_func=restore_default_preset("5.0", "内_LCD_手动"),
    text="恢复内_LCD_手动预设",
    tab_index=tab_index_presets
)
restore_5_0_lcd_ex_auto_button = BaseButton(
    bind_func=restore_default_preset("5.0", "外_LCD_自动"),
    text="恢复外_LCD_自动预设",
    tab_index=tab_index_presets
)
restore_5_0_lcd_ex_manual_button = BaseButton(
    bind_func=restore_default_preset("5.0", "外_LCD_手动"),
    text="恢复外_LCD_手动预设",
    tab_index=tab_index_presets
)
restore_5_0_all_button = BaseButton(
    bind_func=restore_default_preset("5.0"), 
    text="恢复5.0所有默认预设", 
    tab_index=tab_index_presets
)

# 4.0版本预设恢复按钮
restore_4_0_auto_button = BaseButton(
    bind_func=restore_default_preset("4.0", "auto_manual"),
    text="恢复4.0自/手动亮度预设",
    tab_index=tab_index_presets
)
restore_4_0_manual_button = BaseButton(
    bind_func=restore_default_preset("4.0", "manual"),
    text="恢复4.0手动亮度预设",
    tab_index=tab_index_presets
)
restore_4_0_ex_auto_button = BaseButton(
    bind_func=restore_default_preset("4.0", "ex_auto_manual"),
    text="恢复4.0外销自/手动亮度预设",
    tab_index=tab_index_presets
)
restore_4_0_ex_manual_button = BaseButton(
    bind_func=restore_default_preset("4.0", "ex_manual"),
    text="恢复4.0外销手动亮度预设",
    tab_index=tab_index_presets
)
restore_4_0_all_button = BaseButton(
    bind_func=restore_default_preset("4.0"), 
    text="恢复4.0所有默认预设", 
    tab_index=tab_index_presets
)

# 恢复所有预设按钮
restore_all_button = BaseButton(
    bind_func=restore_all_default_presets, 
    text="恢复所有版本默认预设", 
    tab_index=tab_index_presets
)

# 预设配置说明
preset_info_text = "预设配置说明：\n\n" \
                  "1. 点击预设按钮后，会将当前队列中的应用保存为预设\n" \
                  "2. 预设保存在presets_config.json文件中\n" \
                  "3. 预设会在下次选择对应预设时自动加载\n" \
                  "4. 预设可以手动编辑presets_config.json文件修改\n" \
                  "5. 若误操作保存了错误的预设，可以点击恢复按钮恢复默认预设"

# 创建预设配置页面
combine_presets = VerticalFrameCombine([
    VerticalFrameCombine([
        Label(text="5.0版本预设", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_oled_in_auto_button,
            restore_5_0_oled_in_auto_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_oled_in_manual_button,
            restore_5_0_oled_in_manual_button,
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_oled_ex_auto_button,
            restore_5_0_oled_ex_auto_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_oled_ex_manual_button,
            restore_5_0_oled_ex_manual_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_lcd_in_auto_button,
            restore_5_0_lcd_in_auto_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_lcd_in_manual_button,
            restore_5_0_lcd_in_manual_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_lcd_ex_auto_button,
            restore_5_0_lcd_ex_auto_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_5_0_lcd_ex_manual_button,
            restore_5_0_lcd_ex_manual_button
        ], text="", tab_index=tab_index_presets),
        restore_5_0_all_button
    ], tab_index=tab_index_presets, text=""),
    VerticalFrameCombine([
        Label(text="4.0版本预设", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_4_0_auto_button,
            restore_4_0_auto_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_4_0_manual_button,
            restore_4_0_manual_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_4_0_ex_auto_button,
            restore_4_0_ex_auto_button
        ], text="", tab_index=tab_index_presets),
        HorizontalToolsCombine([
            save_4_0_ex_manual_button,
            restore_4_0_ex_manual_button
        ], text="", tab_index=tab_index_presets),
        restore_4_0_all_button
    ], tab_index=tab_index_presets, text=""),
    restore_all_button,
    Label(text=preset_info_text, tab_index=tab_index_presets),
    InputBox(name="输入框")
], tab_index=tab_index_presets, text="预设配置")
q_gui.add_notebook_tool(combine_presets)

# top 头部栏
q_gui.add_banner_tool(BaseBarTool(bind_func=controller.connect_device, name="连接设备"))
# 添加弹窗监控开关（使用普通按钮样式）
q_gui.add_banner_tool(BaseBarTool(bind_func=controller.popup_monitor_switch, name="弹窗监控"))
# 添加打开程序文件夹按钮
q_gui.add_banner_tool(BaseBarTool(bind_func=open_program_folder, name="打开测试结果"))

# 5.0和4.0面板共享设备连接状态，但维持各自的测试逻辑
controller_four.set_adb(controller.adb)  # 共享ADB实例
controller_four.set_main_controller(controller)  # 设置主控制器引用，用于检查设备连接状态

# 第一页 显示亮度降幅5.0
combine_left = VerticalFrameCombine([
    HorizontalToolsCombine([
        RadioObviousToolButtonAutoLine(
                options=[
                    "清空队列",
                    "内_OLED_自动", "内_OLED_手动",
                    "外_OLED_自动", "外_OLED_手动",
                    "内_LCD_自动", "内_LCD_手动",
                    "外_LCD_自动", "外_LCD_手动"
                ], title="预设：", name="RadioObviousTool", bind_func=controller.preinstall_click, tab_index=tab_index_5, max_height=80),
        # RadioObviousToolButton(
        #         options=[
        #             "室内标准",
        #         ], title="测试标准：", name="TestingStandardTool", bind_func=controller.testing_standard_click, tab_index=tab_index_5),
        AutoBrightnessSwitch,
    ], text="", tab_index=tab_index_5),
    CheckObviousToolLine,
    HorizontalToolsCombine([
        InputBox(name="InputPackageName", default="", label_info="输入包名：", tab_index=tab_index_5),
        BaseButton(bind_func=controller.add_queue_package, text="加入任务队列", tab_index=tab_index_5),
    ], text="", tab_index=tab_index_5),
    HorizontalToolsCombine([
        Progressbar(name="进度条",tab_index=tab_index_5),
        RunButton(bind_func=controller.click, tab_index=tab_index_5)
    ], text="", tab_index=tab_index_5)], tab_index=tab_index_5, text="")
q_gui.add_notebook_tool(combine_left)

combine_right = VerticalFrameCombine([add_to_queue_button_5, TaskTool],
                                     title="任务进程", tab_index=tab_index_5)
q_gui.add_notebook_tool(combine_right)

# 第二页 显示亮度降幅4.0
combine_left_four = VerticalFrameCombine([
    HorizontalToolsCombine([
        RadioObviousToolButtonAutoLine(
                options=[
                    "清空队列", "内销自动", "内销手动",
                    "外销自动", "外销手动",
                ], title="预设：", name="RadioObviousToolFour", bind_func=controller_four.preinstall_click, tab_index=tab_index_4, max_height=35),
        # RadioObviousToolButton(
        #         options=[
        #             "室内标准",
        #         ], title="测试标准：", name="TestingStandardTool", bind_func=controller_four.testing_standard_click, tab_index=tab_index_4),
        AutoBrightnessSwitchFour,
    ], text="", tab_index=tab_index_4),
    CheckObviousToolLineFour,
    HorizontalToolsCombine([
        InputBox(name="InputPackageNameFour", default="30,100,300", label_info="输入测试亮度：", tab_index=tab_index_4),
        BaseButton(bind_func=controller_four.add_queue_package, text="确认", tab_index=tab_index_4),
    ], text="", tab_index=tab_index_4),
    HorizontalToolsCombine([
        Progressbar(name="进度条Four",tab_index=tab_index_4),
        RunButton(bind_func=controller_four.click, tab_index=tab_index_4)
    ], text="", tab_index=tab_index_4)], tab_index=tab_index_4, text="")
q_gui.add_notebook_tool(combine_left_four)

combine_right_four = VerticalFrameCombine([add_to_queue_button_4, TaskToolFour],
                                     title="任务进程", tab_index=tab_index_4)
q_gui.add_notebook_tool(combine_right_four)

# 第三页 three
base_5_button = BaseButton(bind_func=controller_three.save_5_config, text="保存配置", tab_index=tab_index_5_config)

combine_config = VerticalFrameCombine([list_5_component, base_5_button], tab_index=tab_index_5_config, text="配置信息")
q_gui.add_notebook_tool(combine_config)

# 第五页的实例 - 任务队列
controller_queue.set_global_instance(globalModel)

# 队列列表框
queue_list_box = CustomTaskQueueDisplay(
    name="QueueListBox", 
    bind_func=lambda x: None,  # 不绑定点击事件
    tab_index=tab_index_queue, 
    max_width=500,   # 增加宽度，让内容更容易看到
    title="队列执行列表 - 点击「开始执行」按钮开始测试"
)

# 当前队列进度条
current_queue_progress = Progressbar(
    name="CurrentQueueProgress",
    title="当前队列进度",
    tab_index=tab_index_queue
)

# 总体进度条
total_queue_progress = Progressbar(
    name="TotalQueueProgress",
    title="总体进度",
    tab_index=tab_index_queue
)

# 开始执行按钮
start_execution_button = RunButton(
    bind_func=controller_queue.execute_queues,
    text="开始执行",
    tab_index=tab_index_queue
)

# 停止执行按钮
stop_execution_button = BaseButton(
    bind_func=lambda args: controller_queue.stop_execution(),
    text="停止执行",
    tab_index=tab_index_queue
)

# 清空队列按钮
clear_queue_button = BaseButton(
    bind_func=lambda args: controller_queue.clear_execution_queue(),
    text="清空队列",
    tab_index=tab_index_queue
)

# 添加实例到全局模型
globalModel.add_instance(queue_list_box)
globalModel.add_instance(current_queue_progress)
globalModel.add_instance(total_queue_progress)

# 设置队列控制器到全局实例
controller_queue.name = "QueueController"
globalModel.add_instance(controller_queue)

# 设置UI组件到队列控制器
controller_queue.set_ui_components(
    progress_bar=current_queue_progress,
    queue_list_box=queue_list_box,
    queue_total_progress=total_queue_progress
)

# 初始化API服务器
api_server = None
if API_AVAILABLE:
    try:
        api_server = APIServer(port=9080, host='127.0.0.1')
        api_server.set_controllers(
            queue_controller=controller_queue,
            main_controller=controller,
            four_controller=controller_four
        )
        logger.info("API服务器初始化完成")
    except Exception as e:
        logger.error(f"API服务器初始化失败: {e}")
        print(f"API服务器初始化失败: {e}")
        api_server = None

# 任务队列页面的布局组合
queue_buttons = HorizontalFrameCombine(
    [start_execution_button, stop_execution_button, clear_queue_button],
    tab_index=tab_index_queue,
    text="操作"
)

# 队列列表和进度条组合到一个垂直框架
queue_content = VerticalFrameCombine(
    [
        queue_list_box,  # 队列列表
        current_queue_progress,  # 当前队列进度条
        total_queue_progress     # 总体进度条
    ],
    tab_index=tab_index_queue,
    title="任务队列"
)

# 添加组件到界面
q_gui.add_notebook_tool(queue_buttons)
q_gui.add_notebook_tool(queue_content)

# 左侧信息栏
# 简单加个简介
q_gui.set_navigation_about(author="梁盛民",
                           version="3.0.1",
                           user_url="http://localhost:8951/?action=talkapi&toUserCode=11171448",
                           url_info="联系方式",
                           other_info=["功耗温升组自动化"])
# 设备连接信息
q_gui.set_navigation_info(title="连接信息", info="无设备连接", name="ConnectDevice")
# 弹窗监控状态
q_gui.set_navigation_info(title="弹窗监控", info="初始化中...", name="PopupMonitorStatus")

# 初始化UI状态
controller.init_ui_status()

# 启动API服务器
if api_server:
    try:
        api_server.start()
        logger.info("API服务器已启动，端口: 9080")
    except Exception as e:
        logger.error(f"启动API服务器失败: {e}")
        print(f"启动API服务器失败: {e}")

# 定义程序退出时的清理函数
def cleanup_on_exit():
    """程序退出时的清理工作"""
    if api_server:
        try:
            api_server.stop()
            logger.info("API服务器已停止")
        except Exception as e:
            logger.error(f"停止API服务器失败: {e}")

# 注册退出处理
import atexit
atexit.register(cleanup_on_exit)

try:
    q_gui.run()
except KeyboardInterrupt:
    print("\n程序被用户中断")
    cleanup_on_exit()
except Exception as e:
    logger.error(f"程序运行错误: {e}")
    cleanup_on_exit()
    raise
